"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { DatePickerWithRange } from "@/components/shared/date-range-picker"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
    StarIcon,
    MapPin,
    Calendar,
    Info,
    Shield,
    Fuel,
    Settings,
    Users,
    Bluetooth,
    Navigation,
    Music,
    Coffee,
    DollarSign,
} from "lucide-react"
import Link from "next/link"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useParams } from "next/navigation"
import whatsappIcon from '@/public/icons/whatsapp-svg.svg';
import callIcon from '@/public/icons/call-svg.svg';
import Image from 'next/image';
import ReactCalendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css'
import { useState } from 'react'
import { useCurrency } from "@/contexts/currency-context"

const vehiclesData = [
    // Cars
    {
        id: "car1",
        type: "car",
        title: "Hyundai Creta 5-Seater 2022",
        priceDay: 520,
        priceWeek: 3450,
        priceMonth: 13650,
        location: "Agadir International Airport, Agadir",
        city: "Agadir",
        images: [
            "/creta1.jpg",
            "/creta2.jpg",
            "/creta3.jpg",
            "/creta4.jpg",
        ],
        rating: 4.9,
        reviews: 234,
        category: "Crossover",
        description: "Rent Hyundai Creta 5-Seater 2022 in Agadir. Premium crossover with automatic transmission, diesel engine, and all modern features.",
        features: {
            bodyType: "Crossover",
            brand: "Hyundai",
            model: "Creta 5-Seater",
            fuelType: "Diesel",
            gearbox: "Auto",
            people: 5,
            trunk: 2,
            year: 2022,
            ac: true,
            engineCapacity: "1.6 Ltrs",
            doors: 4,
            seats: 5,
            color: "Gray / Black",
            deposit: 5000,
            insuranceType: "Comprehensive",
            bluetooth: true,
            gps: true,
            usbCharger: true,
            sunroof: true,
        },
    },
    // Motorcycles
    {
        id: "moto1",
        type: "motorcycle",
        title: "Yamaha MT-07",
        price: 40,
        location: "Marrakech",
        images: [
            "/placeholder.svg?height=400&width=600",
            "/placeholder.svg?height=400&width=600",
        ],
        rating: 4.7,
        reviews: 98,
        category: "Naked",
        description: "Powerful and agile naked bike perfect for city riding and weekend adventures.",
        features: {
            engine: "689cc",
            transmission: "6-speed",
            type: "Naked",
            year: 2023,
        },
    },
    // Add more vehicles as needed
]

// Function to slugify agency name
function slugifyAgencyName(name: string) {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
}

// Mock agencyInfo (replace with real import/context if available)
const agencyInfo = {
    name: "Morocco Premium Rentals",
    logo: "/placeholder.svg?height=100&width=100",
    whatsapp: "+212661234567",
    phone: "+212524123456",
    profileUrl: `/agency/${slugifyAgencyName("Morocco Premium Rentals")}`,
    rentalPolicy: "Rental policy details here...",
}

// Function to get agency rental policy from settings
const getAgencyRentalPolicy = () => {
    const defaultPolicy = [
        "• Drivers must be 21+ years old with valid driver's license",
        "• Security deposit required (varies by vehicle)",
        "• Vehicle must be returned in the same condition as received",
        "• Fuel tank must be returned full",
        "• Late returns may incur additional charges",
        "• Free cancellation up to 48 hours before pickup",
        "• All cars delivered with full tank and should be returned the same"
    ]
    // Only access localStorage on client
    if (typeof window !== 'undefined') {
        const storedPolicy = localStorage.getItem("agencyRentalPolicy")
        if (storedPolicy) {
            return storedPolicy.split('\n').filter(line => line.trim())
        }
    }
    return defaultPolicy
}

// Define a features config at the top of the file:
const featuresConfig = [
    { key: 'bodyType', name: 'Body Type', icon: '/icons/car-body.svg', type: 'select' },
    { key: 'year', name: 'Year Model', icon: '/icons/year.svg', type: 'select' },
    { key: 'fuelType', name: 'Fuel Type', icon: '/icons/gas-svgrepo-com.svg', type: 'select' },
    { key: 'ac', name: 'AC', icon: '/icons/car-air-conditioning-svgrepo-com.svg', type: 'boolean' },
    { key: 'engineCapacity', name: 'Engine Capacity', icon: '/icons/car-engine-svgrepo-com.svg', type: 'input' },
    { key: 'bluetooth', name: 'Bluetooth', icon: '/icons/bluetooth-on-svgrepo-com.svg', type: 'boolean' },
    { key: 'airbags', name: 'Airbags', icon: '/icons/airbags.svg', type: 'boolean' },
    { key: 'gps', name: 'GPS', icon: '/icons/gps.svg', type: 'boolean' },
    { key: 'usbCharger', name: 'USB Charger', icon: '/icons/usb.svg', type: 'boolean' },
    { key: 'sunroof', name: 'Sunroof', icon: '/icons/sunroof.svg', type: 'boolean' },
];

// Mock booked dates for demo
const bookedDates = [
    new Date(2025, 5, 3),
    new Date(2025, 5, 6),
    new Date(2025, 5, 11),
]

// Mock booking details for demo
const bookingDetails = {
    '2025-06-03': { name: 'John Doe', info: 'Booked by John Doe (3-6 June)' },
    '2025-06-06': { name: 'Jane Smith', info: 'Booked by Jane Smith (6 June)' },
    '2025-06-11': { name: 'Ali Ben', info: 'Booked by Ali Ben (11 June)' },
}

export default function VehicleDetailsPage() {
    const params = useParams()
    const vehicleId = params?.id as string
    const vehicle = vehiclesData.find((v) => v.id === vehicleId)
    const [selectedDate, setSelectedDate] = useState<Date | null>(null)
    const [tooltip, setTooltip] = useState<string | null>(null)
    const { convert } = useCurrency()

    if (!vehicle) {
        return <div className="container mx-auto px-4 py-6"><h1 className="text-2xl font-bold">Vehicle Not Found</h1></div>
    }

    const isMotorcycle = vehicle.type === "motorcycle"
    const { features } = vehicle

    // In the component, map the features object to an array:
    const featuresArray = featuresConfig.map(f => ({
        ...f,
        value: vehicle.features[f.key as keyof typeof vehicle.features]
    }));

    // Find recommended cars from the same agency (excluding current car)
    const recommendedCars = vehiclesData.filter(
        (v) => v.id !== vehicle.id && agencyInfo.name === "Morocco Premium Rentals"
    )

    return (
        <div className="container mx-auto px-4 py-6 flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                    <img
                        src={`/car-logos/${vehicle.features.brand?.toLowerCase() || 'default'}.svg`}
                        alt={vehicle.features.brand || 'Car Logo'}
                        className="h-10 w-10 object-contain"
                        onError={(e) => {
                            // Prevent infinite loop by checking if we're already using the default
                            if (e.currentTarget.src !== '/car-logos/default.svg') {
                                e.currentTarget.src = '/car-logos/default.svg'
                            } else {
                                // If default also fails, hide the image
                                e.currentTarget.style.display = 'none'
                            }
                        }}
                    />
                    <span className="text-2xl font-bold">{vehicle.features.brand} {vehicle.features.model}</span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
                    {vehicle.images.map((img, i) => (
                        <img key={i} src={img} alt={vehicle.title + ' image ' + (i + 1)} className="rounded-lg w-full h-40 object-cover" />
                    ))}
                </div>

                <div className="flex items-center gap-2 text-muted-foreground mb-6">
                    <MapPin className="h-4 w-4" />
                    <span>{vehicle.location}</span>
                </div>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Car Overview</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {featuresArray.map((feature, idx) => (
                                feature.key === 'color' ? (
                                    <div key={feature.key} className="flex items-center gap-2">
                                        <div className="w-6 h-6 rounded-full border-2 border-gray-300" style={{ backgroundColor: (feature.value as string)?.toLowerCase() || 'transparent' }}></div>
                                        <span className="font-medium">Color:</span>
                                        <span>{feature.value || 'N/A'}</span>
                                    </div>
                                ) : (
                                    <div key={feature.key} className="flex items-center gap-2">
                                        {feature.icon && <Image src={feature.icon} alt={feature.name} width={24} height={24} />}
                                        <span className="font-medium">{feature.name}:</span>
                                        {feature.type === 'boolean' ? (
                                            feature.value ? <span className="text-green-600">✔</span> : <span className="text-red-500">✘</span>
                                        ) : (
                                            <span>{feature.value}</span>
                                        )}
                                    </div>
                                )
                            ))}
                            <div className="flex items-center gap-2">
                                <Shield className="h-5 w-5 text-primary" />
                                <span className="font-medium">Insurance Type:</span>
                                <span>{vehicle.features.insuranceType || 'N/A'}</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Guarantee Deposit</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-2">
                            <DollarSign className="h-5 w-5 text-primary" />
                            <span className="font-medium">Deposit Amount:</span>
                            <span>{convert(vehicle.features.deposit || 0) || 'N/A'}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">This amount is required as a refundable security deposit for this car.</p>
                    </CardContent>
                </Card>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Rental Policy</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ul className="list-disc pl-5 space-y-1">
                            {getAgencyRentalPolicy().map((policy, idx) => (
                                <li key={idx}>{policy}</li>
                            ))}
                        </ul>
                    </CardContent>
                </Card>

                {/* Reviews & Ratings Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Reviews & Ratings</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Example review, replace with real data */}
                            <div className="border-b pb-4">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className="font-semibold">John Doe</span>
                                    <span className="text-xs text-muted-foreground">April 2024</span>
                                </div>
                                <div className="flex items-center gap-1 mb-1">
                                    {[...Array(5)].map((_, i) => (
                                        <StarIcon key={i} className={`h-4 w-4 ${i < 4 ? 'text-yellow-500' : 'text-gray-300'}`} />
                                    ))}
                                </div>
                                <div className="text-sm">Great car and service. Highly recommend!</div>
                            </div>
                            {/* Add more reviews as needed */}
                        </div>
                    </CardContent>
                </Card>

                {/* Recommended Cars Section */}
                {recommendedCars.length > 0 && (
                    <div className="mt-12">
                        <h2 className="text-2xl font-bold mb-4">Recommended Cars from {agencyInfo.name}</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                            {recommendedCars.map((car) => (
                                <Card key={car.id} className="hover:shadow-lg transition-shadow">
                                    <CardContent className="p-4">
                                        <Link href={`/listings/car-details/${car.id}`} className="block">
                                            <img src={car.images[0]} alt={car.title} className="w-full h-40 object-cover rounded-lg mb-2" />
                                            <div className="font-semibold text-lg mb-1">{car.title}</div>
                                            <div className="text-sm text-muted-foreground mb-1">{car.location}</div>
                                            <div className="text-primary font-bold">MAD {car.priceDay || car.price || 0} / day</div>
                                        </Link>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )}
            </div>
            {/* Right Fixed Container */}
            <div className="w-full lg:w-[340px] flex-shrink-0">
                <div className="bg-white rounded-xl shadow-lg p-6 flex flex-col gap-4 border">
                    {/* Agency Avatar and Name (clickable) */}
                    <Link href={agencyInfo.profileUrl} className="flex flex-col items-center gap-2 group hover:underline mb-2">
                        <Avatar className="h-16 w-16">
                            <AvatarImage src={agencyInfo.logo} />
                            <AvatarFallback>{agencyInfo.name.slice(0, 2).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span className="font-bold text-xl group-hover:underline text-center">{agencyInfo.name}</span>
                    </Link>
                    {/* Contact the agency */}
                    <div className="text-center text-muted-foreground text-sm mb-2">Contact the Agency</div>
                    {/* WhatsApp and Call Buttons (SVG icons) */}
                    <div className="flex gap-3 mb-2">
                        <a
                            href={`https://wa.me/${agencyInfo.whatsapp.replace(/[^\d]/g, "")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex-1 flex items-center justify-center gap-2 rounded-lg py-2 font-semibold text-white bg-[#25D366] hover:bg-[#1ebe57] transition"
                        >
                            <Image src={whatsappIcon} alt="WhatsApp" width={20} height={20} /> WhatsApp
                        </a>
                        <a
                            href={`tel:${agencyInfo.phone}`}
                            className="flex-1 flex items-center justify-center gap-2 rounded-lg py-2 font-semibold text-white bg-[#FF8533] hover:bg-[#FFB733] transition"
                        >
                            <Image src={callIcon} alt="Call" width={20} height={20} /> Call
                        </a>
                    </div>
                    {/* Book directly from here */}
                    <div className="text-center text-muted-foreground text-sm mb-2">Book directly from here</div>
                    {/* Book Now Button */}
                    <Button className="w-full text-lg py-2" size="lg" asChild>
                        <Link href="/confirm-reservation">Book Now</Link>
                    </Button>
                    {/* Pricing (smaller, at the bottom) */}
                    <div className="flex flex-wrap gap-2 justify-center mt-4">
                        <div className="bg-primary text-white rounded-lg px-4 py-2 font-bold text-sm">{convert(vehicle.priceDay || 0)} / day</div>
                        <div className="bg-secondary text-white rounded-lg px-4 py-2 font-bold text-sm">{convert(vehicle.priceWeek || 0)} / week</div>
                        <div className="bg-accent text-white rounded-lg px-4 py-2 font-bold text-sm">{convert(vehicle.priceMonth || 0)} / month</div>
                    </div>
                    {/* Advanced Availability Calendar */}
                    <div className="mt-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Availability Calendar</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="relative">
                                    <ReactCalendar
                                        tileClassName={({ date }: { date: Date }) =>
                                            bookedDates.some(d => d.toDateString() === date.toDateString()) ? 'rc-booked' : 'rc-available'
                                        }
                                        className="w-full text-base"
                                        prev2Label={null}
                                        next2Label={null}
                                        locale="en-US"
                                    />
                                </div>
                                <div className="flex gap-4 mt-2 text-xs">
                                    <span className="inline-block w-3 h-3 bg-red-300 rounded mr-1"></span>Booked
                                    <span className="inline-block w-3 h-3 border border-blue-500 rounded ml-4 mr-1"></span>Today
                                    <span className="inline-block w-3 h-3 bg-gray-100 rounded ml-4 mr-1"></span>Available
                                </div>
                                <style jsx global>{`
                                    .rc-booked {
                                        background: #fecaca !important;
                                        color: #b91c1c !important;
                                        font-weight: bold;
                                        border-radius: 6px;
                                    }
                                    .rc-available {
                                        background: #f3f4f6 !important;
                                        color: #222;
                                        border-radius: 6px;
                                    }
                                    .rc-today {
                                        border: 2px solid #3b82f6 !important;
                                    }
                                    .react-calendar__tile:hover {
                                        background: #dbeafe !important;
                                        color: #1d4ed8 !important;
                                    }
                                    .animate-fade-in {
                                        animation: fadeIn 0.2s;
                                    }
                                    @keyframes fadeIn {
                                        from { opacity: 0; transform: translateY(-10px); }
                                        to { opacity: 1; transform: translateY(0); }
                                    }
                                `}</style>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    )
} 