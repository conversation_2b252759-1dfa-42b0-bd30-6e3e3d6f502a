import Link from "next/link"
import { StarIcon, MapPin, Car } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface AgencyCardProps {
  name: string
  image: string
  location: string
  rating: number
  reviews: number
  carCount: number
}

export function AgencyCard({ name, image, location, rating, reviews, carCount }: AgencyCardProps) {
  return (
    <Link href="/agency/details">
      <Card className="overflow-hidden transition-all hover:shadow-2xl border-2 border-[#1E81B0] bg-gradient-to-br from-white via-[#f0f8ff] to-[#e6f2fa] rounded-2xl min-h-[340px] flex flex-col justify-between">
        <CardContent className="p-8 flex flex-col items-center flex-1 justify-center">
          <Avatar className="h-24 w-24 mb-4 shadow-lg border-4 border-white">
            <AvatarImage alt={name} src={image} />
            <AvatarFallback>{name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <h3 className="font-extrabold text-xl text-[#1E81B0] mb-1 text-center">{name}</h3>
          <div className="flex items-center text-sm text-muted-foreground mb-2">
            <MapPin className="h-4 w-4 mr-1 text-[#1E81B0]" />
            <span>{location}</span>
          </div>
          <div className="flex items-center gap-3 mt-2">
            <Badge
              variant="secondary"
              className="flex items-center gap-1 bg-gradient-to-r from-[#1E81B0] to-[#166089] text-white px-3 py-1 rounded-full text-sm shadow"
            >
              <StarIcon className="h-4 w-4 fill-current text-yellow-400" />
              <span className="font-semibold">{rating} <span className="opacity-70">({reviews})</span></span>
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1 border-[#1E81B0] text-[#1E81B0] bg-white px-3 py-1 rounded-full text-sm shadow">
              <Car className="h-4 w-4" />
              <span className="font-semibold">{carCount} cars</span>
            </Badge>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
