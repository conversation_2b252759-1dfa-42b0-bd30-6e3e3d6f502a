"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { LayoutDashboard, Users, Building2, Car, Settings, LogOut, BarChart3, ShieldCheck, MessageSquare, CreditCard, BookOpen } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

const navItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Agencies",
    href: "/admin/agencies",
    icon: Building2,
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: Users,
  },
  {
    title: "Cars",
    href: "/admin/cars",
    icon: Car,
  },
  {
    title: "Analytics",
    href: "/admin/analytics",
    icon: BarChart3,
  },
  {
    title: "Messages",
    href: "/admin/messages",
    icon: MessageSquare,
  },
  {
    title: "Payment Requests",
    href: "/admin/payment-requests",
    icon: CreditCard,
  },
  {
    title: "Blogs",
    href: "/admin/dashboard/blogs",
    icon: BookO<PERSON>,
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings,
  },
]

export function Sidebar() {
  const pathname = usePathname()
  const { logout, user } = useAuth()

  return (
    <div className="w-64 bg-white border-r min-h-screen p-4">
      <div className="flex items-center gap-2 mb-8 px-2">
        <ShieldCheck className="h-8 w-8 text-primary" />
        <div>
          <h2 className="font-bold text-xl">Admin Panel</h2>
          <p className="text-xs text-muted-foreground">morentcar Management</p>
        </div>
      </div>

      <div className="space-y-1">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-primary",
              pathname === item.href ? "bg-primary/10 text-primary font-medium" : "text-muted-foreground",
            )}
          >
            <item.icon className="h-4 w-4" />
            {item.title}
          </Link>
        ))}
      </div>

      <div className="absolute bottom-4 w-52">
        <div className="border-t pt-4 mt-4">
          <div className="flex items-center gap-3 mb-4 px-3">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              {user && 'name' in user && typeof user.name === "string" && user.name.length > 0 ? user.name.charAt(0).toUpperCase() : <ShieldCheck className="h-4 w-4 text-primary" />}
            </div>
            <div>
              <p className="text-sm font-medium">{user && 'name' in user && typeof user.name === "string" && user.name.length > 0 ? user.name : "Admin"}</p>
              <p className="text-xs text-muted-foreground">{user?.email || "<EMAIL>"}</p>
            </div>
          </div>
          <button
            onClick={logout}
            className="flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm text-muted-foreground transition-all hover:text-primary"
          >
            <LogOut className="h-4 w-4" />
            Logout
          </button>
        </div>
      </div>
    </div>
  )
}
