// App-wide constants
export const APP_CONFIG = {
    name: 'Modrive',
    version: '1.0.0',
    description: 'Car rental platform',
} as const;

export const ROUTES = {
    // Public routes
    home: '/',
    about: '/about',
    contact: '/contact',
    pricing: '/pricing',
    faqs: '/faqs',
    blogs: '/blogs',
    howItWorks: '/how-it-works',
    learnMore: '/learn-more',
    terms: '/terms',
    privacyPolicy: '/privacy-policy',

    // Auth routes
    auth: '/auth',
    verifyEmail: '/verify-email',

    // User routes
    userDashboard: '/user/dashboard',
    userSettings: '/user/settings',
    userNotifications: '/user/notifications',

    // Agency routes
    agencyDashboard: '/agency/dashboard',
    agencyDetails: '/agency/details',
    becomeHost: '/become-host',
    forAgencies: '/for-agencies',

    // Admin routes
    adminDashboard: '/admin/dashboard',
    adminLogin: '/admin/login',
    adminUsers: '/admin/users',
    adminAgencies: '/admin/agencies',
    adminCars: '/admin/cars',
    adminAnalytics: '/admin/analytics',
    adminMessages: '/admin/messages',
    adminSettings: '/admin/settings',
    adminPaymentRequests: '/admin/payment-requests',

    // Listings routes
    listings: '/listings',
    carDetails: '/listings/car-details',

    // Other routes
    confirmReservation: '/confirm-reservation',
    payment: '/payment',
    accessDenied: '/access-denied',
} as const;

export const API_ENDPOINTS = {
    auth: {
        login: '/api/auth/login',
        register: '/api/auth/register',
        logout: '/api/auth/logout',
        verify: '/api/auth/verify',
    },
    users: {
        profile: '/api/users/profile',
        update: '/api/users/update',
    },
    agencies: {
        list: '/api/agencies',
        details: '/api/agencies/:id',
        create: '/api/agencies',
        update: '/api/agencies/:id',
    },
    cars: {
        list: '/api/cars',
        details: '/api/cars/:id',
        create: '/api/cars',
        update: '/api/cars/:id',
        delete: '/api/cars/:id',
    },
    bookings: {
        list: '/api/bookings',
        create: '/api/bookings',
        update: '/api/bookings/:id',
        cancel: '/api/bookings/:id/cancel',
    },
    gps: {
        location: '/api/gps/location',
        track: '/api/gps/track',
    },
} as const;

export const USER_ROLES = {
    USER: 'user',
    AGENCY: 'agency',
    ADMIN: 'admin',
} as const;

export const BOOKING_STATUS = {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    ACTIVE: 'active',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled',
} as const;

export const CAR_STATUS = {
    AVAILABLE: 'available',
    RENTED: 'rented',
    MAINTENANCE: 'maintenance',
    UNAVAILABLE: 'unavailable',
} as const; 