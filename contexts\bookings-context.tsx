import React, { createContext, useContext, useState } from 'react'

export interface Booking {
    id: string
    carId: string
    userId?: string
    agencyId?: string
    customerName: string
    startDate: string
    endDate: string
    status: 'pending' | 'accepted' | 'rejected'
    images?: string[]
    totalAmount?: number
    pickupLocation?: string
    dropoffLocation?: string
    notes?: string
}

interface BookingsContextType {
    bookings: Booking[]
    addBooking: (booking: Omit<Booking, 'id'>) => void
    deleteBooking: (id: string) => void
    updateBooking: (id: string, updates: Partial<Booking>) => void
}

const BookingsContext = createContext<BookingsContextType | undefined>(undefined)

export const BookingsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [bookings, setBookings] = useState<Booking[]>([])

    // Generate unique booking ID
    const generateBookingId = (): string => {
        const timestamp = Date.now().toString(36)
        const random = Math.random().toString(36).substr(2, 5)
        return `BK-${timestamp}-${random}`.toUpperCase()
    }

    const addBooking = (booking: Omit<Booking, 'id'>) => {
        const newBooking: Booking = {
            ...booking,
            id: generateBookingId()
        }
        setBookings(prev => [...prev, newBooking])
    }

    const deleteBooking = (id: string) => setBookings(prev => prev.filter(b => b.id !== id))
    const updateBooking = (id: string, updates: Partial<Booking>) => setBookings(prev => prev.map(b => b.id === id ? { ...b, ...updates } : b))

    return (
        <BookingsContext.Provider value={{ bookings, addBooking, deleteBooking, updateBooking }}>
            {children}
        </BookingsContext.Provider>
    )
}

export function useBookings() {
    const ctx = useContext(BookingsContext)
    if (!ctx) throw new Error('useBookings must be used within a BookingsProvider')
    return ctx
} 