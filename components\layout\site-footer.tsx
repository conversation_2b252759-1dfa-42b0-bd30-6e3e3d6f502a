import Link from "next/link"
import { Car, MapPin, Mail, Phone, Facebook, Instagram, Twitter, Linkedin } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

export function SiteFooter() {
  return (
    <footer className="bg-black text-white">
      <div className="container mx-auto px-4 py-10">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Car className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">morentcar</span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span className="text-sm">123, Rue Mohammed V, Marrakech, Morocco</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="text-sm">+212 524 123 456</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/about">
                  About Us
                </Link>
              </li>
              {/* Temporarily hidden
              <li>
                <Link href="/pricing" className="hover:underline">Pricing</Link>
              </li>
              */}
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/privacy-policy">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/terms">
                  Terms & Conditions
                </Link>
              </li>
              <li>
                <Link href="/how-it-works" className="hover:underline">How It Works</Link>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Community</h3>
            <ul className="space-y-2">
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/contact">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/faqs">
                  FAQs
                </Link>
              </li>
              <li>
                <Link className="text-sm hover:text-primary transition-colors" href="/blogs">
                  Blogs
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Subscribe Newsletter</h3>
            <p className="text-sm">Our Estimated Global Carbon Emissions by Investing in Carbon</p>
            <div className="flex">
              <Input
                type="email"
                placeholder="Email Address"
                className="rounded-r-none bg-transparent border-white/20"
              />
              <Button className="rounded-l-none bg-primary hover:bg-primary/90">Subscribe</Button>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Social Media</h3>
              <div className="flex gap-4">
                <Link
                  href="#"
                  className="h-8 w-8 rounded-full bg-white/10 flex items-center justify-center hover:bg-primary transition-colors"
                >
                  <Facebook className="h-4 w-4" />
                </Link>
                <Link
                  href="#"
                  className="h-8 w-8 rounded-full bg-white/10 flex items-center justify-center hover:bg-primary transition-colors"
                >
                  <Instagram className="h-4 w-4" />
                </Link>
                <Link
                  href="#"
                  className="h-8 w-8 rounded-full bg-white/10 flex items-center justify-center hover:bg-primary transition-colors"
                >
                  <Twitter className="h-4 w-4" />
                </Link>
                <Link
                  href="#"
                  className="h-8 w-8 rounded-full bg-white/10 flex items-center justify-center hover:bg-primary transition-colors"
                >
                  <Linkedin className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-center border-t border-white/10 mt-8 pt-8">
          <p className="text-sm text-white/60">
            Copyright © 2025 <span className="text-primary">morentcar</span>. All Rights Reserved
          </p>
          <div className="flex gap-2 mt-4 md:mt-0">
            <img src="/placeholder.svg?height=30&width=40" alt="Visa" className="h-8" />
            <img src="/placeholder.svg?height=30&width=40" alt="Mastercard" className="h-8" />
            <img src="/placeholder.svg?height=30&width=40" alt="Apple Pay" className="h-8" />
            <img src="/placeholder.svg?height=30&width=40" alt="PayPal" className="h-8" />
          </div>
        </div>
      </div>
    </footer>
  )
}
