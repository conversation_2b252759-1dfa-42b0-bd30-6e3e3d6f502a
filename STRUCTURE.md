# Modrive Project Structure

This document outlines the improved monorepo structure for the Modrive car rental platform.

## 📁 Directory Structure

```
modrivet/
├── app/                          # Next.js App Router (Frontend + API Routes)
│   ├── (auth)/                   # Authentication pages
│   ├── (dashboard)/              # Dashboard pages
│   ├── (public)/                 # Public pages
│   ├── api/                      # API routes (if any)
│   └── globals.css
├── components/                   # React components
│   ├── ui/                       # Reusable UI components (shadcn/ui)
│   ├── features/                 # Feature-specific components
│   │   ├── auth/                 # Authentication components
│   │   ├── booking/              # Booking components
│   │   ├── car/                  # Car management components
│   │   └── agency/               # Agency management components
│   ├── layout/                   # Layout components
│   └── shared/                   # Shared components
├── lib/                          # Utilities and helpers
│   ├── api/                      # API client and utilities
│   │   └── client.ts             # Centralized API client
│   ├── constants/                # App constants
│   │   └── app.ts                # Routes, endpoints, statuses
│   ├── helpers/                  # Helper functions
│   │   └── date-helpers.ts       # Date utility functions
│   ├── validation/               # Validation schemas
│   │   └── auth.ts               # Authentication validation
│   └── utils.ts                  # General utilities (cn function)
├── hooks/                        # Custom React hooks
│   └── use-auth.ts               # Authentication hook
├── services/                     # Business logic & API services
│   └── auth.service.ts           # Authentication service
├── types/                        # TypeScript type definitions
│   ├── shared/                   # Shared types across features
│   │   ├── auth.ts               # Authentication types
│   │   ├── booking.ts            # Booking types
│   │   ├── car.ts                # Car types
│   │   ├── payment.ts            # Payment types
│   │   └── index.ts              # Type exports
│   ├── api/                      # API-specific types
│   └── components/               # Component-specific types
├── contexts/                     # React contexts (existing)
├── i18n/                         # Internationalization (existing)
├── public/                       # Static assets (existing)
└── [config files]                # Configuration files
```

## 🏗️ Architecture Principles

### 1. **Feature-Based Organization**
- Components are organized by feature rather than type
- Each feature has its own directory with related components
- Shared components are separated from feature-specific ones

### 2. **Separation of Concerns**
- **Services**: Handle API calls and business logic
- **Hooks**: Manage state and side effects
- **Components**: Handle UI rendering
- **Types**: Define data structures
- **Utils**: Provide helper functions

### 3. **Type Safety**
- Centralized type definitions in `types/shared/`
- Feature-specific types in `types/api/` and `types/components/`
- Consistent type exports through index files

### 4. **API Management**
- Centralized API client in `lib/api/client.ts`
- Service layer for business logic
- Consistent error handling and response formatting

## 📦 Key Files Explained

### **API Client** (`lib/api/client.ts`)
- Centralized HTTP client with authentication
- Automatic token management
- File upload support
- Error handling

### **Constants** (`lib/constants/app.ts`)
- Route definitions
- API endpoints
- User roles and statuses
- App configuration

### **Services** (`services/`)
- Business logic encapsulation
- API call abstraction
- Data transformation
- Error handling

### **Hooks** (`hooks/`)
- Custom React hooks for state management
- Reusable logic across components
- Authentication state management

### **Types** (`types/shared/`)
- Shared TypeScript interfaces
- Consistent data structures
- Type safety across the application

## 🔄 Migration Guide

### **Phase 1: Create New Structure**
- ✅ Create new directories
- ✅ Set up API client
- ✅ Create shared types
- ✅ Add validation schemas
- ✅ Move components to feature-based directories
- ✅ Update import paths
- ✅ Create index files for clean exports

### **Phase 2: Move Existing Components**
- ✅ Move components to feature-based directories
- ✅ Update imports across the codebase
- ⏳ Refactor components to use new services (optional - can keep existing auth context)

### **Phase 3: Add New Features**
- Implement GPS tracking services
- Add booking management
- Create agency dashboard features

### **Phase 4: Testing & Optimization**
- Add unit tests for services
- Implement error boundaries
- Optimize performance

## 🚀 Benefits

1. **Scalability**: Easy to add new features without cluttering
2. **Maintainability**: Related code is grouped together
3. **Reusability**: Shared components and utilities are clearly separated
4. **Type Safety**: Centralized type definitions
5. **Team Collaboration**: Different developers can work on different features
6. **Testing**: Easier to write and organize tests

## 📝 Usage Examples

### **Using the API Client**
```typescript
import apiClient from '@/lib/api/client';

// GET request
const cars = await apiClient.get('/api/cars', { status: 'available' });

// POST request
const booking = await apiClient.post('/api/bookings', bookingData);

// File upload
const result = await apiClient.uploadFile('/api/upload', file, (progress) => {
  console.log(`Upload progress: ${progress}%`);
});
```

### **Using Services**
```typescript
import authService from '@/services/auth.service';

// Login
const response = await authService.login({ email, password });

// Get profile
const user = await authService.getProfile();
```

### **Using Custom Hooks**
```typescript
import { useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();
  
  // Component logic
}
```

### **Using Types**
```typescript
import type { User, Booking, Car } from '@/types/shared';

interface Props {
  user: User;
  bookings: Booking[];
  cars: Car[];
}
```

## 🔧 Configuration

### **Environment Variables**
```env
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=Modrive
```

### **TypeScript Configuration**
- Strict mode enabled
- Path aliases configured
- Shared types available globally

This structure provides a solid foundation for building a scalable car rental platform with features like GPS tracking, booking management, and agency dashboards. 