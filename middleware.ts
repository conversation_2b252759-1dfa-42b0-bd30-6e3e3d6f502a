import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { Database } from '@/types/supabase'

export async function middleware(request: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient<Database>({ req: request, res })

  // Get the pathname
  const path = request.nextUrl.pathname

  // Refresh session if expired - required for Server Components
  const { data: { session } } = await supabase.auth.getSession()

  // Protect all /admin/* routes (except /admin-login)
  if (path.startsWith("/admin") && path !== "/admin-login") {
    // Check if admin is authenticated via cookie
    const adminAuthenticated = request.cookies.get("adminAuthenticated")?.value === "true"

    // If not authenticated, redirect to admin-login
    if (!adminAuthenticated) {
      return NextResponse.redirect(new URL("/admin-login", request.url))
    }

    // Additional security: verify admin session with <PERSON><PERSON><PERSON>
    if (session) {
      const { data: user } = await supabase
        .from('users')
        .select('role')
        .eq('id', session.user.id)
        .single()

      // If user is not admin, clear cookie and redirect
      if (user?.role !== 'admin') {
        const response = NextResponse.redirect(new URL("/admin-login", request.url))
        response.cookies.delete("adminAuthenticated")
        return response
      }
    } else {
      // No session but has admin cookie - clear cookie and redirect
      const response = NextResponse.redirect(new URL("/admin-login", request.url))
      response.cookies.delete("adminAuthenticated")
      return response
    }
  }

  // Restrict admin-login access to specific route only
  if (path === "/admin-login") {
    // Only allow access from the exact /admin-login path
    // This prevents access from other routes or direct navigation
    const referer = request.headers.get('referer')
    const origin = request.nextUrl.origin

    // Allow direct access to /admin-login or from same origin
    if (!referer || referer.startsWith(origin)) {
      // Check if already authenticated admin
      if (session) {
        const { data: user } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single()

        if (user?.role === 'admin') {
          return NextResponse.redirect(new URL("/admin/dashboard", request.url))
        }
      }
    }
  }

  // Protect agency routes
  if (path.startsWith("/agency") && path !== "/agency/register") {
    if (!session) {
      return NextResponse.redirect(new URL("/auth/login", request.url))
    }

    // Check if user has agency role
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single()

    if (user?.role !== 'agency') {
      return NextResponse.redirect(new URL("/access-denied", request.url))
    }
  }

  // Protect user dashboard routes
  if (path.startsWith("/user/dashboard")) {
    if (!session) {
      return NextResponse.redirect(new URL("/auth/login", request.url))
    }
  }

  // Add admin-login to valid routes
  const validRoutes = [
    "/",
    "/admin-login",
    "/admin",  // Allow /admin for backward compatibility
    "/agency",
    "/user",
    "/auth",
    "/verify-email",
    "/payment",
    "/pricing",
    "/listings",
    "/about",
    "/contact",
    "/faqs",
    "/privacy-policy",
    "/terms",
    "/how-it-works",
    "/for-agencies",
    "/become-host",
    "/learn-more",
    "/blogs",
    "/confirm-reservation"
  ]

  // Check if the path is a valid route or starts with a valid route prefix
  const isValidRoute = validRoutes.some(route =>
    path === route || path.startsWith(route + "/")
  )

  // Check if it's a Next.js internal route or static file
  const isNextJsRoute = path.startsWith("/_next") ||
    path.startsWith("/api") ||
    path.includes(".")

  // If it's not a valid route and not a Next.js internal route, redirect to home
  if (!isValidRoute && !isNextJsRoute) {
    return NextResponse.redirect(new URL("/", request.url))
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

