import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"

export function middleware(request: NextRequest) {
  // Get the pathname
  const path = request.nextUrl.pathname

  // Protect all /admin/* routes
  if (path.startsWith("/admin") && path !== "/admin-login") {
    // Check if admin is authenticated
    const adminAuthenticated = request.cookies.get("adminAuthenticated")?.value === "true"

    // If not authenticated, redirect to admin-login
    if (!adminAuthenticated) {
      return NextResponse.redirect(new URL("/admin-login", request.url))
    }
  }

  // Add admin-login to valid routes
  const validRoutes = [
    "/",
    "/admin-login",
    "/admin",  // Allow /admin for backward compatibility
    "/agency",
    "/user",
    "/auth",
    "/verify-email",
    "/payment",
    "/pricing",
    "/listings",
    "/about",
    "/contact",
    "/faqs",
    "/privacy-policy",
    "/terms",
    "/how-it-works",
    "/for-agencies",
    "/become-host",
    "/learn-more",
    "/blogs",
    "/confirm-reservation"
  ]

  // Check if the path is a valid route or starts with a valid route prefix
  const isValidRoute = validRoutes.some(route =>
    path === route || path.startsWith(route + "/")
  )

  // Check if it's a Next.js internal route or static file
  const isNextJsRoute = path.startsWith("/_next") ||
    path.startsWith("/api") ||
    path.includes(".")

  // If it's not a valid route and not a Next.js internal route, redirect to home
  if (!isValidRoute && !isNextJsRoute) {
    return NextResponse.redirect(new URL("/", request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

