"use client"

import { useAuth } from "@/contexts/auth-context"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { CheckCircle2, XCircle, CreditCard, Bell } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

export function NotificationsPanel() {
    const { user, markNotificationAsRead } = useAuth()

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case "booking_confirmed":
                return <CheckCircle2 className="h-5 w-5 text-green-500" />
            case "booking_cancelled":
                return <XCircle className="h-5 w-5 text-red-500" />
            case "payment_received":
                return <CreditCard className="h-5 w-5 text-blue-500" />
            default:
                return <Bell className="h-5 w-5 text-gray-500" />
        }
    }

    const getNotificationColor = (type: string) => {
        switch (type) {
            case "booking_confirmed":
                return "bg-green-50 border-green-200"
            case "booking_cancelled":
                return "bg-red-50 border-red-200"
            case "payment_received":
                return "bg-blue-50 border-blue-200"
            default:
                return "bg-gray-50 border-gray-200"
        }
    }

    if (!user?.notifications?.length) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle>Notifications</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                        No notifications yet
                    </div>
                </CardContent>
            </Card>
        )
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Notifications</CardTitle>
            </CardHeader>
            <CardContent>
                <ScrollArea className="h-[400px] pr-4">
                    <div className="space-y-4">
                        {user.notifications
                            .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
                            .map((notification) => (
                                <div
                                    key={notification.id}
                                    className={`p-4 rounded-lg border ${getNotificationColor(
                                        notification.type
                                    )} ${notification.read ? "opacity-60" : ""}`}
                                >
                                    <div className="flex items-start gap-3">
                                        {getNotificationIcon(notification.type)}
                                        <div className="flex-1">
                                            <p className="text-sm font-medium">{notification.message}</p>
                                            <p className="text-xs text-muted-foreground mt-1">
                                                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                                            </p>
                                        </div>
                                        {!notification.read && (
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                className="h-6 px-2 text-xs"
                                                onClick={() => markNotificationAsRead(notification.id)}
                                            >
                                                Mark as read
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            ))}
                    </div>
                </ScrollArea>
            </CardContent>
        </Card>
    )
} 