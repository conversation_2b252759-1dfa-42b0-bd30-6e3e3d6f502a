'use client'

import { useEffect, useState } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, User } from "lucide-react"
import Link from "next/link"
import { DatabaseService } from "@/services/database.service"
import { Database } from "@/types/supabase"

type Blog = Database['public']['Tables']['blogs']['Row'] & {
    users?: {
        first_name: string
        last_name: string
        avatar: string | null
    } | null
}

export default function BlogsPage() {
    const [blogs, setBlogs] = useState<Blog[]>([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        loadBlogs()
    }, [])

    const loadBlogs = async () => {
        try {
            const { data, error } = await DatabaseService.getBlogs({ published: true })
            if (error) throw error
            setBlogs(data || [])
        } catch (error) {
            console.error('Error loading blogs:', error)
        } finally {
            setLoading(false)
        }
    }

    if (loading) return <div className="text-center py-12">Loading blogs...</div>
    if (!blogs.length) return <div className="text-center py-12">No blogs published yet.</div>

    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-7xl mx-auto">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
                    <p className="text-muted-foreground max-w-2xl mx-auto">
                        Discover travel tips, vehicle guides, and the latest updates about car and motorcycle rentals in Morocco
                    </p>
                </div>

                {/* Featured Post */}
                {blogs[0] && (
                    <div className="mb-12">
                        <Card className="overflow-hidden">
                            <div className="grid md:grid-cols-2">
                                <div className="relative h-[300px] md:h-auto">
                                    <img
                                        src={blogs[0].image}
                                        alt={blogs[0].title}
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                <div className="p-6">
                                    <div className="flex items-center gap-4 mb-4">
                                        <Badge variant="secondary">Featured</Badge>
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Calendar className="h-4 w-4 mr-1" />
                                            {new Date(blogs[0].published_at || blogs[0].created_at).toLocaleDateString()}
                                        </div>
                                    </div>
                                    <h2 className="text-2xl font-bold mb-2">{blogs[0].title}</h2>
                                    <p className="text-muted-foreground mb-4">
                                        {blogs[0].excerpt || blogs[0].content.slice(0, 120)}...
                                    </p>
                                    <div className="flex items-center gap-4 mb-4">
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Clock className="h-4 w-4 mr-1" />
                                            {Math.max(1, Math.round(blogs[0].content.length / 800))} min read
                                        </div>
                                    </div>
                                    <Button asChild>
                                        <Link href={`/blogs/${blogs[0].slug}`}>Read More</Link>
                                    </Button>
                                </div>
                            </div>
                        </Card>
                    </div>
                )}

                {/* Blog Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {blogs.slice(1).map((post) => (
                        <Card key={post.id} className="overflow-hidden">
                            <div className="relative h-48">
                                <img
                                    src={post.image}
                                    alt={post.title}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            <CardHeader>
                                <div className="flex items-center gap-4 mb-2">
                                    <Badge variant="secondary">Blog</Badge>
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        {new Date(post.createdAt).toLocaleDateString()}
                                    </div>
                                </div>
                                <h3 className="text-xl font-bold">{post.title}</h3>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground mb-4">{post.content.slice(0, 100)}...</p>
                            </CardContent>
                            <CardFooter>
                                <Button variant="outline" asChild className="w-full">
                                    <Link href={`/blogs/${post.id}`}>Read More</Link>
                                </Button>
                            </CardFooter>
                        </Card>
                    ))}
                </div>

                {/* Newsletter Subscription */}
                <div className="mt-12 text-center">
                    <h2 className="text-2xl font-bold mb-4">Subscribe to Our Newsletter</h2>
                    <p className="text-muted-foreground mb-6">
                        Get the latest travel tips and updates delivered to your inbox
                    </p>
                    <div className="flex max-w-md mx-auto gap-2">
                        <input
                            type="email"
                            placeholder="Enter your email"
                            className="flex-1 px-4 py-2 rounded-md border"
                        />
                        <Button>Subscribe</Button>
                    </div>
                </div>
            </div>
        </div>
    )
} 