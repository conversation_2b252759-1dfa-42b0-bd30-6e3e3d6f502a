'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Clock, User } from "lucide-react"
import Link from "next/link"

const blogPosts = [
    {
        id: 1,
        title: "Top 10 Scenic Drives in Morocco",
        excerpt: "Discover the most breathtaking routes across Morocco, from coastal highways to mountain passes. Perfect for your next road trip adventure.",
        content: "Morocco offers some of the most spectacular driving routes in North Africa. From the winding roads of the Atlas Mountains to the coastal highways along the Mediterranean, each route offers unique landscapes and experiences. The Tizi n'Tichka pass, connecting Marrakech to Ouarzazate, is particularly famous for its dramatic views and challenging curves...",
        author: "<PERSON>",
        date: "March 15, 2024",
        readTime: "5 min read",
        category: "Travel",
        image: "/placeholder.svg?height=400&width=600&text=Scenic+Drive",
        tags: ["Road Trips", "Morocco", "Travel Guide"]
    },
    {
        id: 2,
        title: "Electric Vehicles in Morocco: The Future is Now",
        excerpt: "Exploring the growing electric vehicle infrastructure in Morocco and what it means for the future of transportation.",
        content: "Morocco is making significant strides in sustainable transportation. With the world's largest solar power plant and increasing investment in EV infrastructure, the country is positioning itself as a leader in green mobility. Major cities are seeing more charging stations, and the government is offering incentives for electric vehicle adoption...",
        author: "Fatima Zahra",
        date: "March 10, 2024",
        readTime: "4 min read",
        category: "Technology",
        image: "/placeholder.svg?height=400&width=600&text=Electric+Vehicle",
        tags: ["Electric Vehicles", "Sustainability", "Technology"]
    },
    {
        id: 3,
        title: "Essential Car Maintenance Tips for Moroccan Roads",
        excerpt: "Learn how to keep your vehicle in top condition while navigating Morocco's diverse road conditions.",
        content: "Maintaining your vehicle in Morocco requires special attention due to the country's varied climate and road conditions. From the hot desert regions to the humid coastal areas, different environments demand different maintenance approaches. Regular checks of your air conditioning system, tire pressure, and engine cooling are crucial...",
        author: "Mohammed Alami",
        date: "March 5, 2024",
        readTime: "6 min read",
        category: "Maintenance",
        image: "/placeholder.svg?height=400&width=600&text=Car+Maintenance",
        tags: ["Car Care", "Maintenance", "Tips"]
    },
    {
        id: 4,
        title: "Motorcycle Adventures: Exploring Morocco on Two Wheels",
        excerpt: "A comprehensive guide to motorcycle touring in Morocco, from route planning to essential gear.",
        content: "Motorcycle touring in Morocco offers an unparalleled sense of freedom and adventure. The country's diverse landscapes, from the Sahara Desert to the Atlas Mountains, provide endless opportunities for exploration. This guide covers everything from choosing the right motorcycle to packing essential gear and planning your route...",
        author: "Yasmine Kaddouri",
        date: "February 28, 2024",
        readTime: "7 min read",
        category: "Adventure",
        image: "/placeholder.svg?height=400&width=600&text=Motorcycle+Tour",
        tags: ["Motorcycles", "Adventure", "Travel"]
    },
    {
        id: 5,
        title: "Car Rental Insurance: What You Need to Know",
        excerpt: "Understanding different types of car rental insurance and choosing the right coverage for your needs.",
        content: "When renting a car in Morocco, understanding your insurance options is crucial. This guide breaks down the different types of coverage available, from basic liability to comprehensive protection. Learn about excess reduction options, what's typically included in basic coverage, and when to consider additional protection...",
        author: "Karim Bennis",
        date: "February 20, 2024",
        readTime: "5 min read",
        category: "Insurance",
        image: "/placeholder.svg?height=400&width=600&text=Insurance",
        tags: ["Insurance", "Car Rental", "Guide"]
    },
    {
        id: 6,
        title: "Family Road Trips in Morocco: A Complete Guide",
        excerpt: "Planning the perfect family road trip in Morocco, including kid-friendly stops and safety tips.",
        content: "Morocco offers fantastic opportunities for family road trips, with plenty of attractions suitable for all ages. From the beaches of Agadir to the historic medinas of Fes, there's something for everyone. This guide includes tips for keeping children entertained during long drives, family-friendly accommodation options, and must-visit attractions...",
        author: "Leila Moussa",
        date: "February 15, 2024",
        readTime: "8 min read",
        category: "Family",
        image: "/placeholder.svg?height=400&width=600&text=Family+Trip",
        tags: ["Family Travel", "Road Trips", "Guide"]
    }
]

export default function BlogsPage() {
    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-7xl mx-auto">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4">Our Blog</h1>
                    <p className="text-muted-foreground max-w-2xl mx-auto">
                        Discover travel tips, vehicle guides, and the latest updates about car and motorcycle rentals in Morocco
                    </p>
                </div>

                {/* Featured Post */}
                <div className="mb-12">
                    <Card className="overflow-hidden">
                        <div className="grid md:grid-cols-2">
                            <div className="relative h-[300px] md:h-auto">
                                <img
                                    src={blogPosts[0].image}
                                    alt={blogPosts[0].title}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            <div className="p-6">
                                <div className="flex items-center gap-4 mb-4">
                                    <Badge variant="secondary">{blogPosts[0].category}</Badge>
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        {blogPosts[0].date}
                                    </div>
                                </div>
                                <h2 className="text-2xl font-bold mb-2">{blogPosts[0].title}</h2>
                                <p className="text-muted-foreground mb-4">{blogPosts[0].excerpt}</p>
                                <div className="flex items-center gap-4 mb-4">
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <User className="h-4 w-4 mr-1" />
                                        {blogPosts[0].author}
                                    </div>
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Clock className="h-4 w-4 mr-1" />
                                        {blogPosts[0].readTime}
                                    </div>
                                </div>
                                <Button asChild>
                                    <Link href={`/blogs/${blogPosts[0].id}`}>Read More</Link>
                                </Button>
                            </div>
                        </div>
                    </Card>
                </div>

                {/* Blog Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {blogPosts.slice(1).map((post) => (
                        <Card key={post.id} className="overflow-hidden">
                            <div className="relative h-48">
                                <img
                                    src={post.image}
                                    alt={post.title}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            <CardHeader>
                                <div className="flex items-center gap-4 mb-2">
                                    <Badge variant="secondary">{post.category}</Badge>
                                    <div className="flex items-center text-sm text-muted-foreground">
                                        <Calendar className="h-4 w-4 mr-1" />
                                        {post.date}
                                    </div>
                                </div>
                                <h3 className="text-xl font-bold">{post.title}</h3>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground mb-4">{post.excerpt}</p>
                                <div className="flex flex-wrap gap-2 mb-4">
                                    {post.tags.map((tag) => (
                                        <Badge key={tag} variant="outline">
                                            {tag}
                                        </Badge>
                                    ))}
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button variant="outline" asChild className="w-full">
                                    <Link href={`/blogs/${post.id}`}>Read More</Link>
                                </Button>
                            </CardFooter>
                        </Card>
                    ))}
                </div>

                {/* Newsletter Subscription */}
                <div className="mt-12 text-center">
                    <h2 className="text-2xl font-bold mb-4">Subscribe to Our Newsletter</h2>
                    <p className="text-muted-foreground mb-6">
                        Get the latest travel tips and updates delivered to your inbox
                    </p>
                    <div className="flex max-w-md mx-auto gap-2">
                        <input
                            type="email"
                            placeholder="Enter your email"
                            className="flex-1 px-4 py-2 rounded-md border"
                        />
                        <Button>Subscribe</Button>
                    </div>
                </div>
            </div>
        </div>
    )
} 