import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <header className="px-4 lg:px-6 h-14 flex items-center border-b">
        <Link className="flex items-center justify-center" href="/">
          <span className="ml-2 text-xl font-bold">morentcar</span>
        </Link>
        <nav className="ml-auto flex items-center gap-4 sm:gap-6">
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="/listings">
            Browse Cars
          </Link>
          <Link className="text-sm font-medium hover:underline underline-offset-4" href="/agency/dashboard">
            Agency Dashboard
          </Link>
          <Button variant="outline" size="sm">
            Sign In
          </Button>
          <Button size="sm">Sign Up</Button>
        </nav>
      </header>

      <main className="flex-1">
        <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-r from-red-500 to-orange-500 text-white">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center text-white">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  Discover Morocco on Wheels
                </h1>
                <p className="mx-auto max-w-[700px] text-white/90 md:text-xl">
                  Rent from trusted agencies across Morocco
                </p>
              </div>
              <div className="w-full max-w-sm">
                <Button className="w-full" size="lg">
                  Browse Cars
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <footer className="border-t py-6 md:py-0">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            © 2025 morentcar. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
