# Supabase Setup Guide for Agency Functionality

## Overview

This guide will help you set up your Supabase database to support the complete agency functionality for your car rental platform.

## Prerequisites

1. A Supabase account and project
2. Supabase CLI installed (optional but recommended)
3. Your Next.js application ready

## Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Note down your project URL and anon key

## Step 2: Set Up Database Schema

### Option A: Using SQL Editor (Recommended)

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the entire content from `SUPABASE_SCHEMA.sql`
4. Execute the script

### Option B: Using Supabase CLI

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Apply the schema
supabase db push
```

## Step 3: Configure Authentication

### Email Confirmation Settings

1. Go to Authentication > Settings in your Supabase dashboard
2. Configure email templates for:
   - Email confirmation
   - Password reset
   - Magic link

### Social Auth (Optional)

Configure social authentication providers:
- Google
- Facebook
- Apple

## Step 4: Set Up Storage Buckets

The schema automatically creates these storage buckets:
- `car-images` - For car photos
- `agency-logos` - For agency logos
- `user-avatars` - For user profile pictures

### Storage Policies

The schema includes policies that:
- Allow public read access to images
- Restrict uploads to authenticated users
- Allow agency owners to manage their content

## Step 5: Configure Environment Variables

Create a `.env.local` file with your Supabase credentials:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Step 6: Test the Setup

### Test Agency Registration

1. Start your development server
2. Navigate to `/become-host` or your agency registration page
3. Register a new agency account
4. Verify the user is created with `role: 'agency'`
5. Check that an agency record is created in the `agencies` table

### Test Agency Dashboard Access

1. Login with the agency account
2. Navigate to `/agency/dashboard`
3. Verify all tabs work:
   - Cars management
   - Bookings management
   - Reviews management
   - GPS tracking
   - Coupons management
   - Analytics
   - Settings

## Step 7: Implement GPS Tracking

### Option A: Real-time GPS Updates

```typescript
// In your GPS tracking component
import { supabase } from '@/lib/supabase/client'

// Subscribe to GPS updates for agency cars
const subscribeToGPSUpdates = (agencyId: string) => {
  return supabase
    .channel('gps-updates')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'gps_tracking',
        filter: `car_id=in.(SELECT id FROM cars WHERE agency_id='${agencyId}')`
      },
      (payload) => {
        console.log('GPS Update:', payload.new)
        // Update your UI with new GPS data
      }
    )
    .subscribe()
}
```

### Option B: External GPS Service Integration

If using an external GPS service:

```typescript
// GPS service integration
export class GPSService {
  static async updateCarLocation(carId: string, location: GPSData) {
    const { data, error } = await supabase
      .from('gps_tracking')
      .insert({
        car_id: carId,
        latitude: location.latitude,
        longitude: location.longitude,
        speed: location.speed,
        heading: location.heading,
        battery_level: location.batteryLevel,
        is_online: location.isOnline
      })
    
    return { data, error }
  }
}
```

## Step 8: Implement Booking Flow

### User Creates Booking

```typescript
// When user creates a booking
const createBooking = async (bookingData: BookingCreateData) => {
  const { data, error } = await supabase
    .from('bookings')
    .insert({
      user_id: user.id,
      car_id: bookingData.carId,
      agency_id: car.agency_id, // Get from car data
      start_date: bookingData.startDate,
      end_date: bookingData.endDate,
      pickup_location: bookingData.pickupLocation,
      return_location: bookingData.returnLocation,
      total_price: calculateTotalPrice(bookingData),
      status: 'pending'
    })
    .select()
    .single()
  
  return { data, error }
}
```

### Agency Responds to Booking

```typescript
// Agency accepts/rejects booking
const respondToBooking = async (bookingId: string, status: 'confirmed' | 'rejected') => {
  const { data, error } = await supabase
    .from('bookings')
    .update({ status })
    .eq('id', bookingId)
    .select()
    .single()
  
  return { data, error }
}
```

## Step 9: Implement Review System

### User Submits Review

```typescript
const submitReview = async (reviewData: ReviewData) => {
  const { data, error } = await supabase
    .from('reviews')
    .insert({
      booking_id: reviewData.bookingId,
      user_id: user.id,
      agency_id: reviewData.agencyId,
      car_id: reviewData.carId,
      rating: reviewData.rating,
      comment: reviewData.comment,
      status: 'pending' // Requires agency approval
    })
    .select()
    .single()
  
  return { data, error }
}
```

### Agency Approves/Rejects Review

```typescript
const moderateReview = async (reviewId: string, status: 'approved' | 'rejected') => {
  const { data, error } = await supabase
    .from('reviews')
    .update({ status })
    .eq('id', reviewId)
    .select()
    .single()
  
  return { data, error }
}
```

## Step 10: Implement Coupon System

### Agency Creates Coupon

```typescript
const createCoupon = async (couponData: CouponData) => {
  const { data, error } = await supabase
    .from('coupons')
    .insert({
      agency_id: agency.id,
      code: couponData.code,
      discount_type: couponData.discountType,
      discount_value: couponData.discountValue,
      min_amount: couponData.minAmount,
      max_discount: couponData.maxDiscount,
      usage_limit: couponData.usageLimit,
      valid_from: couponData.validFrom,
      valid_until: couponData.validUntil
    })
    .select()
    .single()
  
  return { data, error }
}
```

## Step 11: Security Considerations

### Row Level Security (RLS)

The schema includes comprehensive RLS policies that ensure:
- Users can only access their own data
- Agencies can only manage their own content
- Public data (cars, agencies) is readable by all
- Sensitive operations require proper authentication

### API Security

```typescript
// Always verify user permissions
const verifyAgencyAccess = async (agencyId: string) => {
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) throw new Error('Not authenticated')
  
  const { data: agency } = await supabase
    .from('agencies')
    .select('id')
    .eq('id', agencyId)
    .eq('user_id', user.id)
    .single()
  
  if (!agency) throw new Error('Access denied')
  
  return agency
}
```

## Step 12: Performance Optimization

### Database Indexes

The schema includes optimized indexes for:
- User lookups by email
- Agency lookups by user_id
- Car searches by location and price
- Booking queries by date range
- GPS tracking by timestamp

### Query Optimization

```typescript
// Use efficient queries with proper joins
const getAgencyDashboardData = async (agencyId: string) => {
  const { data, error } = await supabase
    .from('agencies')
    .select(`
      *,
      cars (
        id,
        brand,
        model,
        status,
        daily_rate
      ),
      bookings (
        id,
        status,
        total_price,
        created_at
      ),
      reviews (
        id,
        rating,
        status
      )
    `)
    .eq('id', agencyId)
    .single()
  
  return { data, error }
}
```

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**: Ensure user is authenticated and has proper permissions
2. **Foreign Key Violations**: Check that referenced records exist
3. **Storage Upload Failures**: Verify bucket policies and file size limits
4. **Real-time Subscription Issues**: Check channel configuration and permissions

### Debug Queries

```sql
-- Check user roles
SELECT id, email, role FROM users WHERE email = '<EMAIL>';

-- Check agency data
SELECT * FROM agencies WHERE user_id = 'user-uuid';

-- Check booking relationships
SELECT 
  b.id,
  u.email as user_email,
  a.agency_name,
  c.brand || ' ' || c.model as car_name
FROM bookings b
JOIN users u ON b.user_id = u.id
JOIN agencies a ON b.agency_id = a.id
JOIN cars c ON b.car_id = c.id;
```

## Next Steps

1. Implement email notifications for booking requests
2. Add payment processing integration
3. Set up automated GPS tracking updates
4. Implement advanced analytics and reporting
5. Add mobile app support with real-time updates

## Support

For issues with this setup:
1. Check Supabase documentation
2. Review the SQL schema for any syntax errors
3. Verify environment variables are correctly set
4. Test with the provided example queries 