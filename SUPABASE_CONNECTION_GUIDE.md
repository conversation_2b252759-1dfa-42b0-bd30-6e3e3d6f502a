# Supabase Connection Guide

This guide will help you complete the Supabase setup for your car rental platform with both database and authentication.

## ✅ What's Already Done

1. **Supabase packages installed**: All necessary Supabase packages are already installed
2. **Environment variables configured**: Your `.env.local` file has the correct Supabase credentials
3. **Client and server setup**: Supabase client configurations are ready
4. **Auth context enhanced**: Updated with better error handling and type safety
5. **TypeScript types**: Complete database types generated
6. **Services created**: Authentication, database, storage, and real-time services
7. **Middleware updated**: Route protection with Supabase authentication
8. **Auth pages**: Callback, reset password, and forgot password pages created

## 🔧 Manual Steps Required

### 1. Apply Database Schema

1. Go to your Supabase Dashboard: https://supabase.com/dashboard/project/sagtwjbwgfgvzulnsmhc
2. Navigate to **SQL Editor**
3. Copy the entire content from `SUPABASE_SCHEMA.sql`
4. Paste and execute the script

This will create:
- All necessary tables (users, agencies, cars, bookings, reviews, payments, notifications, etc.)
- Row Level Security (RLS) policies
- Storage buckets for images and documents
- Database functions and triggers

### 2. Configure Authentication Settings

1. In your Supabase Dashboard, go to **Authentication > Settings**
2. Configure the following:

#### Site URL
- Set to: `http://localhost:3000` (for development)
- For production, update to your domain

#### Redirect URLs
Add these URLs:
- `http://localhost:3000/auth/callback`
- `http://localhost:3000/auth/reset-password`

#### Email Templates (Optional)
Customize the email templates for:
- Confirm signup
- Reset password
- Magic link

### 3. Set Up Storage Buckets

The schema should create these buckets automatically, but verify they exist:

1. Go to **Storage** in your Supabase Dashboard
2. Ensure these buckets exist:
   - `car-images` (for car photos)
   - `agency-logos` (for agency logos)
   - `avatars` (for user profile pictures)
   - `documents` (for verification documents)

3. Configure bucket policies:
   - Make `car-images` and `agency-logos` publicly readable
   - Make `avatars` publicly readable
   - Keep `documents` private with RLS policies

### 4. Configure OAuth Providers (Optional)

If you want to enable social login:

1. Go to **Authentication > Providers**
2. Enable and configure:
   - Google OAuth
   - Facebook OAuth
   - GitHub OAuth (if desired)

## 🚀 Testing the Setup

### 1. Test Authentication

```bash
npm run dev
```

1. Navigate to `/auth/login`
2. Try creating a new account
3. Check if user profile is created in the `users` table
4. Test login/logout functionality

### 2. Test Database Operations

Use the provided services:

```typescript
import { DatabaseService } from '@/services/database.service'
import { SupabaseAuthService } from '@/services/supabase-auth.service'

// Test getting cars
const { data: cars, error } = await DatabaseService.getCars()

// Test user registration
const { data, error } = await SupabaseAuthService.signUp(
  '<EMAIL>',
  'password123',
  {
    first_name: 'John',
    last_name: 'Doe',
    role: 'user'
  }
)
```

### 3. Test File Uploads

```typescript
import { StorageService } from '@/services/storage.service'

// Test image upload
const { data: imageUrl, error } = await StorageService.uploadCarImage(file, carId)
```

### 4. Test Real-time Features

```typescript
import { RealtimeService } from '@/services/realtime.service'

// Subscribe to booking updates
RealtimeService.subscribeToUserBookings(userId, (payload) => {
  console.log('Booking update:', payload)
})
```

## 📁 New Files Created

1. **Services**:
   - `services/supabase-auth.service.ts` - Enhanced authentication
   - `services/database.service.ts` - Database operations
   - `services/realtime.service.ts` - Real-time subscriptions
   - Enhanced `services/storage.service.ts` - File uploads

2. **Auth Pages**:
   - `app/auth/callback/page.tsx` - OAuth callback handler
   - `app/auth/reset-password/page.tsx` - Password reset
   - `app/auth/forgot-password/page.tsx` - Forgot password

3. **Types**:
   - Updated `types/supabase.ts` - Complete database types

4. **Middleware**:
   - Enhanced `middleware.ts` - Route protection with Supabase

## 🔐 Security Features Implemented

1. **Row Level Security (RLS)**: All tables have proper RLS policies
2. **Route Protection**: Middleware protects authenticated routes
3. **Role-based Access**: Different access levels for users, agencies, and admins
4. **Secure File Uploads**: Proper validation and storage policies
5. **JWT Authentication**: Secure session management

## 🎯 Next Steps

After completing the manual steps above:

1. **Test all authentication flows**
2. **Create your first agency and cars**
3. **Test booking functionality**
4. **Set up email notifications**
5. **Configure payment processing**
6. **Add real-time features to your UI**

## 🆘 Troubleshooting

### Common Issues:

1. **Authentication not working**:
   - Check environment variables
   - Verify redirect URLs in Supabase dashboard
   - Check browser console for errors

2. **Database queries failing**:
   - Ensure schema is applied correctly
   - Check RLS policies
   - Verify user permissions

3. **File uploads not working**:
   - Check storage bucket policies
   - Verify file size and type restrictions
   - Check network connectivity

### Getting Help:

1. Check the Supabase documentation: https://supabase.com/docs
2. Review the generated types in `types/supabase.ts`
3. Check the browser console and network tab for errors
4. Verify your environment variables are correct

## 🎉 You're All Set!

Once you complete these steps, your car rental platform will be fully connected to Supabase with:
- ✅ Complete authentication system
- ✅ Secure database with RLS
- ✅ File upload capabilities
- ✅ Real-time features
- ✅ Type-safe operations
- ✅ Role-based access control

Happy coding! 🚗💨
