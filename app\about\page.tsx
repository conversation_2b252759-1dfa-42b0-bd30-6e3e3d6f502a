import { <PERSON><PERSON> } from "@/components/ui/button"
import { Car, Clock, Shield, MapPin } from "lucide-react"
import Link from "next/link"

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">About morentcar</h1>
          <p className="text-xl text-muted-foreground">
            Connecting travelers with trusted car rental agencies across Morocco
          </p>
        </div>

        <div className="prose prose-lg max-w-none">
          <p className="text-lg mb-4">
            Founded in 2020, morentcar was born from a passion to transform the car rental experience in Morocco. We
            believe that exploring this beautiful country should be accessible, reliable, and enjoyable for everyone.
          </p>

          <p className="text-lg mb-4">
            Our platform connects travelers with carefully vetted car rental agencies, ensuring you get the best
            vehicles, competitive prices, and exceptional service wherever your journey takes you in Morocco.
          </p>

          <p className="text-lg mb-8">
            Today, morentcar has grown to become Morocco's leading car rental marketplace, serving thousands of satisfied
            customers and partnering with the most reputable agencies across the country.
          </p>

          <div className="my-8">
            <img
              src="/images/partner-section.png"
              alt="morentcar Team"
              className="w-full h-64 object-cover rounded-lg"
            />
          </div>

          <h2 className="text-3xl font-bold text-center mb-12">Why Choose morentcar</h2>
        </div>

        {/* Our Mission Section */}
        <div className="bg-muted rounded-xl p-12 mb-20">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
            <p className="text-xl">
              "To provide travelers with the most reliable, transparent, and convenient car rental experience in Morocco,
              while supporting local businesses and promoting sustainable tourism."
            </p>
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl border shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full w-fit mb-6">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Verified Agencies</h3>
              <p className="text-muted-foreground">
                We thoroughly vet all rental agencies on our platform to ensure they meet our high standards for service,
                vehicle quality, and reliability.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl border shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full w-fit mb-6">
                <Car className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Quality Vehicles</h3>
              <p className="text-muted-foreground">
                From economy cars to luxury vehicles, our platform offers a diverse fleet of well-maintained vehicles to
                suit every need and budget.
              </p>
            </div>
            <div className="bg-white p-8 rounded-xl border shadow-sm">
              <div className="bg-primary/10 p-3 rounded-full w-fit mb-6">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-bold mb-3">Seamless Experience</h3>
              <p className="text-muted-foreground">
                Our platform is designed to make booking a car as simple as possible, with transparent pricing, easy
                comparisons, and a streamlined reservation process.
              </p>
            </div>
          </div>
        </div>

        {/* Our Presence Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Our Presence in Morocco</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <p className="text-lg text-muted-foreground mb-6">
                morentcar has established a strong presence across Morocco, with partner agencies in all major cities and
                tourist destinations. Our extensive network ensures that you can find the perfect car rental option no
                matter where your Moroccan adventure takes you.
              </p>
              <p className="text-lg text-muted-foreground mb-6">
                From the bustling streets of Casablanca to the serene landscapes of the Sahara, our partner agencies are
                strategically located to serve you wherever your journey leads. We're proud to support local businesses
                while providing travelers with reliable transportation options.
              </p>
            </div>
            <div className="rounded-xl overflow-hidden">
              <img
                src="/images/map-morocco.png"
                alt="morentcar Presence in Morocco"
                className="w-full h-auto object-cover max-w-[60%] max-h-[60%] mx-auto"
              />
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary to-secondary text-white rounded-xl p-12 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Experience Morocco on Wheels?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied travelers who have discovered Morocco with morentcar.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-primary hover:bg-white/90" asChild>
              <Link href="/listings">Browse Cars</Link>
            </Button>
            <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white" asChild>
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
