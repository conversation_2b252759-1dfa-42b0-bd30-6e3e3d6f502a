# Admin Setup Guide

## Admin Secret Key Configuration

The admin portal uses a secret key for additional security. Here's how to configure it:

### Current Secret Key
The current admin secret key is: `admin-secret-123`

### How to Change the Secret Key

1. **Locate the auth context file:**
   ```
   contexts/auth-context.tsx
   ```

2. **Find the secret key validation:**
   ```typescript
   // Check secret key
   if (secretKey !== "admin-secret-123") {
     // Increment failed attempts
     attempts.count++
     attempts.lastAttempt = now
     ADMIN_LOGIN_ATTEMPTS.set(email, attempts)
     return { user: null, error: "INVALID_SECRET_KEY" }
   }
   ```

3. **Replace the secret key:**
   Change `"admin-secret-123"` to your new secret key in both places where it appears:
   - Line ~210: Secret key validation
   - Line ~220: Admin user creation (if using fallback)

4. **Security Recommendations:**
   - Use a strong, random string (at least 16 characters)
   - Include uppercase, lowercase, numbers, and special characters
   - Store the secret key in environment variables in production
   - Never commit the actual secret key to version control

### Example of a Strong Secret Key
```
MyS3cr3tK3y2024!@#$%^&*()
```

### Admin Login Credentials
- **Email:** <EMAIL>
- **Password:** password123
- **Secret Key:** admin-secret-123 (or your custom key)

### Rate Limiting
- Maximum login attempts: 5
- Cooldown period: 15 minutes
- After 5 failed attempts, the IP is blocked for 15 minutes

### Security Features
1. **Rate Limiting:** Prevents brute force attacks
2. **Secret Key:** Additional layer of security
3. **Cookie-based Authentication:** Secure session management
4. **Separate Admin Portal:** Isolated from regular user authentication

### Production Deployment
For production deployment, consider:
1. Using environment variables for the secret key
2. Implementing proper session management
3. Adding IP whitelisting for admin access
4. Using HTTPS for all admin communications
5. Implementing two-factor authentication (2FA) 