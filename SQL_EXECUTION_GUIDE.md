# 🔧 SQL Execution Guide - Fixed for Hosted Supabase

## ✅ **Issue Fixed**
The error `permission denied to set parameter "app.jwt_secret"` has been resolved. This parameter is only needed for self-hosted Supabase and is automatically managed in hosted Supabase.

## 📋 **Step-by-Step Execution**

### **Method 1: Execute Complete Script (Recommended)**

1. **Go to your Supabase Dashboard**: https://supabase.com/dashboard/project/sagtwjbwgfgvzulnsmhc
2. **Navigate to SQL Editor**
3. **Copy the ENTIRE content** from the updated `SUPABASE_SCHEMA.sql` file
4. **Paste and execute** the script

The script now includes:
- ✅ Removed problematic `app.jwt_secret` line
- ✅ Safe storage bucket creation (won't fail if buckets exist)
- ✅ All tables, policies, and functions

### **Method 2: Execute in Sections (If Method 1 Fails)**

If you encounter any issues with the complete script, execute these sections separately:

#### **Section 1: Extensions and Tables**
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Then copy and paste all the CREATE TABLE statements
-- (users, agencies, cars, bookings, payments, reviews, etc.)
```

#### **Section 2: Indexes**
```sql
-- Copy and paste all the CREATE INDEX statements
```

#### **Section 3: RLS Policies**
```sql
-- Copy and paste all the ALTER TABLE ENABLE ROW LEVEL SECURITY
-- and CREATE POLICY statements
```

#### **Section 4: Functions and Triggers**
```sql
-- Copy and paste all the CREATE FUNCTION and CREATE TRIGGER statements
```

#### **Section 5: Storage Buckets**
```sql
-- Copy and paste the storage bucket creation and policies
```

## 🚨 **Common Issues and Solutions**

### **Issue 1: Extension Already Exists**
```
ERROR: extension "uuid-ossp" already exists
```
**Solution**: This is normal and safe to ignore. The `IF NOT EXISTS` clause handles this.

### **Issue 2: Table Already Exists**
```
ERROR: relation "users" already exists
```
**Solution**: If you've run parts of the script before, you may need to:
1. Drop existing tables first, OR
2. Skip the table creation and run only the policies

### **Issue 3: Storage Bucket Already Exists**
```
ERROR: duplicate key value violates unique constraint
```
**Solution**: The updated script now handles this automatically with conditional inserts.

### **Issue 4: Policy Already Exists**
```
ERROR: policy "policy_name" for table "table_name" already exists
```
**Solution**: Drop the existing policy first:
```sql
DROP POLICY IF EXISTS "policy_name" ON table_name;
```

## ✅ **Verification Steps**

After successful execution, verify these were created:

### **1. Check Tables**
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

Should show: agencies, bookings, cars, coupons, gps_tracking, notifications, payments, reviews, users

### **2. Check Storage Buckets**
Go to **Storage** in your Supabase dashboard and verify these buckets exist:
- car-images (public)
- agency-logos (public)  
- avatars (public)
- documents (private)

### **3. Check RLS Policies**
```sql
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;
```

### **4. Test Admin Access**
Create an admin user in **Authentication > Users**:
- Email: `<EMAIL>`
- Password: (your choice)
- The trigger should automatically create the profile in `public.users`

## 🎯 **Expected Results**

After successful execution, you'll have:

✅ **Complete Database Schema**: All tables with proper relationships
✅ **Guest Booking Support**: Bookings work with or without user registration
✅ **Role-Based Security**: Users, agencies, and admins have proper permissions
✅ **File Upload System**: Storage buckets with proper access controls
✅ **Real-time Triggers**: Automatic updates for agency stats and user profiles
✅ **Admin Dashboard Ready**: Full admin access to all platform data

## 🆘 **If You Still Get Errors**

1. **Copy the exact error message**
2. **Note which line/section caused the error**
3. **Try executing the script in smaller sections**
4. **Check if you have the necessary permissions in your Supabase project**

## 🚀 **Next Steps After Successful Execution**

1. **Create admin user** in Authentication dashboard
2. **Test agency registration** through your app
3. **Test guest booking** functionality
4. **Verify file uploads** work properly
5. **Check admin dashboard** has full access

Your database is now ready for production use! 🎉
