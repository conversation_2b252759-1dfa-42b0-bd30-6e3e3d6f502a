'use client'

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import dynamic from "next/dynamic"
import "react-quill/dist/quill.snow.css"

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false })

interface BlogFormState {
    title: string
    content: string
    image: File | null
}

export default function AdminBlogsPage() {
    const [form, setForm] = useState<BlogFormState>({ title: "", content: "", image: null })
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [preview, setPreview] = useState<string | null>(null)
    const router = useRouter()

    function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
        const { name, value } = e.target
        setForm((prev) => ({ ...prev, [name]: value }))
    }

    function handleContentChange(value: string) {
        setForm((prev) => ({ ...prev, content: value }))
    }

    function handleImageChange(e: React.ChangeEvent<HTMLInputElement>) {
        const file = e.target.files?.[0] || null
        setForm((prev) => ({ ...prev, image: file }))
        if (file) setPreview(URL.createObjectURL(file))
        else setPreview(null)
    }

    function handleSubmit(e: React.FormEvent) {
        e.preventDefault()
        setIsSubmitting(true)
        // TODO: Implement API call to create blog post
        setTimeout(() => {
            setIsSubmitting(false)
            setForm({ title: "", content: "", image: null })
            setPreview(null)
            // router.push("/admin/dashboard/blogs") // Optionally redirect or show success
        }, 1000)
    }

    return (
        <div className="max-w-2xl mx-auto p-6">
            <h1 className="text-2xl font-bold mb-4">Create Blog Post</h1>
            <form onSubmit={handleSubmit} className="space-y-4">
                <Input
                    name="title"
                    placeholder="Blog Title"
                    value={form.title}
                    onChange={handleChange}
                    required
                />
                <div>
                    <label className="block mb-1 font-medium">Content</label>
                    <ReactQuill
                        value={form.content}
                        onChange={handleContentChange}
                        theme="snow"
                        className="bg-white"
                        style={{ minHeight: 180 }}
                    />
                </div>
                <div>
                    <label className="block mb-1 font-medium">Image</label>
                    <Input type="file" accept="image/*" onChange={handleImageChange} />
                    {preview && (
                        <img src={preview} alt="Preview" className="mt-2 rounded w-full max-h-64 object-cover" />
                    )}
                </div>
                <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Publishing..." : "Publish Blog Post"}
                </Button>
            </form>
        </div>
    )
} 