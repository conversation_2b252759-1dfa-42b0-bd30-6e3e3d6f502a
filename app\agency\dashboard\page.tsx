"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Edit,
  Info,
  Plus,
  Trash,
  Car,
  Calendar,
  DollarSign,
  Users,
  Star,
  ThumbsUp,
  ThumbsDown,
  CheckCircle,
  Upload,
  X,
  LayoutDashboard,
  Settings,
  FileText,
  MessageSquare,
  AreaChart,
  BadgePercent,
  User,
  TrendingUp,
  MapPin,
  Clock,
  Eye,
  Filter,
  Search,
  Download,
  RefreshCw,
  BarChart3,
  PieChart,
  Activity,
  Zap,
  Shield,
  Award,
  MessageCircle,
  Instagram,
  Facebook,
} from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { AuthGuard } from "@/components/features/auth/auth-guard"
import { useI18n } from "@/i18n/i18n-provider"
import { DashboardLayout } from "@/components/layouts/dashboard-layout"
import { useRouter, useSearchParams } from "next/navigation"
import { DatePickerWithRange } from "@/components/shared/date-range-picker"
import { addDays } from "date-fns"
import { Select, SelectItem, SelectTrigger, SelectValue, SelectContent } from "@/components/ui/select"
import DatePicker from "react-datepicker"
import "react-datepicker/dist/react-datepicker.css"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog"
import { useBookings } from '@/contexts/bookings-context'
import type { Booking } from '@/contexts/bookings-context'
import { useAuth } from '@/contexts/auth-context'
import { DateRange } from "react-day-picker"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface CarType {
  id: string
  make: string
  model: string
  year: number
  transmission: string
  price: number
  mileage: number
  insuranceType: string
  deposit: number
  color: string
  location?: {
    lat: number
    lng: number
    address: string
  }
}

interface BookingRequest {
  id: string
  carId: string
  startDate: string
  endDate: string
  customerName: string
  status: "pending" | "accepted" | "rejected"
}

interface Review {
  id: string
  customerName: string
  customerAvatar: string
  rating: number
  date: string
  comment: string
  status: "pending" | "approved" | "rejected"
}

const moroccanCities = [
  "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla",
  "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune",
  "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi",
  "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
].sort((a, b) => a.localeCompare(b))
const timeOptions = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, "0")}:00`);

const sidebarNavItems = [
  {
    title: "Cars",
    href: "/agency/dashboard?tab=cars",
    icon: <Car className="h-4 w-4" />,
  },
  {
    title: "Bookings",
    href: "/agency/dashboard?tab=bookings",
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    title: "Reviews",
    href: "/agency/dashboard?tab=reviews",
    icon: <MessageSquare className="h-4 w-4" />,
  },
  {
    title: "GPS Tracking",
    href: "/agency/dashboard?tab=gps",
    icon: <FileText className="h-4 w-4" />,
  },
  {
    title: "Coupons",
    href: "/agency/dashboard?tab=coupons",
    icon: <DollarSign className="h-4 w-4" />,
  },
  {
    title: "Analytics",
    href: "/agency/dashboard?tab=analytics",
    icon: <AreaChart className="h-4 w-4" />,
  },
  {
    title: "Settings",
    href: "/agency/dashboard?tab=settings",
    icon: <Settings className="h-4 w-4" />,
  },
]

export default function AgencyDashboardPage() {
  const { t } = useI18n()
  const router = useRouter()
  const searchParams = useSearchParams()
  const tab = searchParams.get("tab") || "cars"
  const message = searchParams.get("message")
  const [selectedCity, setSelectedCity] = useState<string>("")
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [cars, setCars] = useState<CarType[]>([
    {
      id: "CAR-AGY-001-001",
      make: "Toyota",
      model: "Corolla",
      year: 2022,
      transmission: "automatic",
      price: 750,
      mileage: 15000,
      insuranceType: "comprehensive",
      deposit: 5000,
      color: "Red",
      location: {
        lat: 30.4278,
        lng: -9.5981,
        address: "Agadir, Morocco"
      }
    },
    {
      id: "CAR-AGY-001-002",
      make: "Honda",
      model: "Civic",
      year: 2023,
      transmission: "automatic",
      price: 800,
      mileage: 8000,
      insuranceType: "premium",
      deposit: 6000,
      color: "Blue",
      location: {
        lat: 31.6295,
        lng: -7.9811,
        address: "Marrakech, Morocco"
      }
    },
    {
      id: "CAR-AGY-001-003",
      make: "Mercedes",
      model: "C-Class",
      year: 2021,
      transmission: "automatic",
      price: 1500,
      mileage: 20000,
      insuranceType: "premium",
      deposit: 10000,
      color: "Black",
      location: {
        lat: 33.9716,
        lng: -6.8498,
        address: "Rabat, Morocco"
      }
    },
  ])

  const { bookings, addBooking, deleteBooking, updateBooking } = useBookings()

  const [reviews, setReviews] = useState<Review[]>([
    {
      id: "review-1",
      customerName: "John Doe",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      date: "March 2025",
      comment:
        "Premium Auto Rentals provided exceptional service from start to finish. The Tesla Model 3 I rented was spotless and fully charged. Their staff was professional and accommodating. I highly recommend this agency!",
      status: "approved",
    },
    {
      id: "review-2",
      customerName: "Michael Rodriguez",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      date: "February 2025",
      comment:
        "Rented the BMW 3 Series for a business trip and it was perfect. The car was delivered on time and the pick-up process was smooth. Premium Auto Rentals has great customer service and a fantastic selection of vehicles.",
      status: "approved",
    },
    {
      id: "review-3",
      customerName: "Sarah Johnson",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "January 2025",
      comment:
        "Great experience with Premium Auto Rentals. The Toyota Corolla was clean and well-maintained. The booking process was straightforward and the staff was helpful. Will definitely use their services again.",
      status: "pending",
    },
  ])

  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([
    {
      id: "BK-1703123456-001",
      carId: "CAR-AGY-001-001",
      startDate: "2025-01-15",
      endDate: "2025-01-18",
      customerName: "Ahmed Alami",
      status: "pending",
    },
    {
      id: "BK-1703123457-002",
      carId: "CAR-AGY-001-002",
      startDate: "2025-01-20",
      endDate: "2025-01-25",
      customerName: "Fatima Zahra",
      status: "accepted",
    },
    {
      id: "BK-1703123458-003",
      carId: "CAR-AGY-001-003",
      startDate: "2025-01-22",
      endDate: "2025-01-24",
      customerName: "Youssef Benali",
      status: "rejected",
    },
  ])

  const [isLoading, setIsLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")

  // Enhanced stats with animations
  const stats = [
    {
      title: "Total Cars",
      value: cars.length.toString(),
      change: "+12%",
      changeType: "positive" as const,
      icon: Car,
      color: "bg-blue-500",
      gradient: "from-blue-500 to-blue-600",
    },
    {
      title: "Pending Bookings",
      value: bookings.filter(b => b.status === "pending").length.toString(),
      change: "+8%",
      changeType: "positive" as const,
      icon: Calendar,
      color: "bg-green-500",
      gradient: "from-green-500 to-green-600",
    },
    {
      title: "Monthly Revenue",
      value: "MAD 45,200",
      change: "+15%",
      changeType: "positive" as const,
      icon: DollarSign,
      color: "bg-purple-500",
      gradient: "from-purple-500 to-purple-600",
    },
    {
      title: "Average Rating",
      value: "4.8",
      change: "+0.2",
      changeType: "positive" as const,
      icon: Star,
      color: "bg-orange-500",
      gradient: "from-orange-500 to-orange-600",
    },
  ]

  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [bookingId, setBookingId] = useState("")
  const [showVerification, setShowVerification] = useState(false)
  const [showCreateBookingModal, setShowCreateBookingModal] = useState(false)
  const [newBooking, setNewBooking] = useState({
    customerName: "",
    customerPhone: "",
    customerEmail: "",
    carId: "",
    startDate: "",
    endDate: "",
    pickupLocation: "",
    dropoffLocation: "",
    totalAmount: 0,
    notes: ""
  })

  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null)
  const [bookingDetailsModalOpen, setBookingDetailsModalOpen] = useState(false)
  const [enlargedImage, setEnlargedImage] = useState<string | null>(null)

  const [selectedCarForGPS, setSelectedCarForGPS] = useState<string>("")
  const [mapCenter, setMapCenter] = useState({ lat: 31.7917, lng: -7.0926 }) // Morocco center

  // Get agency name from localStorage
  const [agencyName, setAgencyName] = useState<string>("")

  useEffect(() => {
    const storedAgencyName = localStorage.getItem("agencyName") || "Premium Auto Rentals"
    setAgencyName(storedAgencyName)
  }, [])

  // Debounced function to save agency name
  const saveAgencyName = (name: string) => {
    localStorage.setItem("agencyName", name)
  }

  // Debounce the save function
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (agencyName.trim()) {
        saveAgencyName(agencyName.trim())
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [agencyName])

  useEffect(() => {
    if (message === "complete-registration") {
      toast.success("Welcome to your agency dashboard! Complete your profile to get started.")
    }
  }, [message])

  const deleteCar = (carId: string) => {
    setCars(cars.filter(car => car.id !== carId))
    toast.success("Car deleted successfully")
  }

  const handleSaveSettings = () => {
    // Save rental policy to localStorage for car details pages
    const rentalTermsElement = document.getElementById('rental-terms') as HTMLTextAreaElement
    if (rentalTermsElement) {
      localStorage.setItem("agencyRentalPolicy", rentalTermsElement.value)
    }

    toast.success("Settings saved successfully!")
    setShowSuccessModal(true)
  }

  const handleReviewAction = (reviewId: string, action: "approve" | "reject") => {
    setReviews(reviews.map(review =>
      review.id === reviewId
        ? { ...review, status: action === "approve" ? "approved" : "rejected" }
        : review
    ))
    toast.success(`Review ${action}d successfully`)
  }

  const handleCreateBooking = () => {
    setShowCreateBookingModal(true)
  }

  const handleSubmitBooking = () => {
    if (!newBooking.customerName || !newBooking.carId || !newBooking.startDate || !newBooking.endDate) {
      toast.error("Please fill in all required fields")
      return
    }

    const booking: Booking = {
      id: `BK-${Date.now()}-${Math.random().toString(36).substr(2, 3)}`,
      carId: newBooking.carId,
      customerName: newBooking.customerName,
      startDate: newBooking.startDate,
      endDate: newBooking.endDate,
      status: "pending",
    }

    addBooking(booking)
    setShowCreateBookingModal(false)
    setNewBooking({
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      carId: "",
      startDate: "",
      endDate: "",
      pickupLocation: "",
      dropoffLocation: "",
      totalAmount: 0,
      notes: ""
    })
    toast.success("Booking created successfully")
  }

  const handleBookingAction = (bookingId: string, action: "accept" | "reject") => {
    updateBooking(bookingId, { status: action === "accept" ? "accepted" : "rejected" })
    toast.success(`Booking ${action === "accept" ? "accepted" : "rejected"} successfully`)
  }

  const handleViewBookingDetails = (booking: Booking) => {
    setSelectedBooking(booking)
    setBookingDetailsModalOpen(true)
  }

  const handleImageClick = (imageUrl: string) => {
    setEnlargedImage(imageUrl)
  }

  const closeEnlargedImage = () => {
    setEnlargedImage(null)
  }

  const filteredCars = cars.filter(car =>
    car.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.id.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === "all" || booking.status === filterStatus
    return matchesSearch && matchesStatus
  })

  // Success Modal Component
  const SuccessModal = () => (
    <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            Settings Saved
          </DialogTitle>
          <DialogDescription>
            Your agency settings have been updated successfully.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button onClick={() => setShowSuccessModal(false)} className="w-full">
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )

  return (
    <AuthGuard requiredRole="agency">
      <DashboardLayout
        sidebarNavItems={sidebarNavItems}
        title={`Welcome, ${agencyName}`}
      >
        <div className="flex-1 space-y-6 p-6 md:p-8">
          {/* Enhanced Header */}
          <div className="flex flex-col space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Welcome, {agencyName}
                </h1>
                <p className="text-muted-foreground">
                  Manage your fleet, bookings, and grow your business
                </p>
              </div>
            </div>

            {/* Enhanced Stats Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Total Cars</p>
                      <p className="text-2xl font-bold">{cars.length}</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+12%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Car className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Pending Bookings</p>
                      <p className="text-2xl font-bold">{bookings.filter(b => b.status === "pending").length}</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+8%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Calendar className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Monthly Revenue</p>
                      <p className="text-2xl font-bold">MAD 45,200</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+15%</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <DollarSign className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="group hover:shadow-lg transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                      <p className="text-2xl font-bold">4.8</p>
                      <div className="flex items-center text-sm">
                        <TrendingUp className="mr-1 h-4 w-4 text-green-600" />
                        <span className="text-green-600">+0.2</span>
                        <span className="text-muted-foreground ml-1">from last month</span>
                      </div>
                    </div>
                    <div className="p-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 text-white group-hover:scale-110 transition-transform duration-300">
                      <Star className="h-6 w-6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Enhanced Tabs */}
          <Tabs value={tab} className="space-y-6">
            <TabsContent value="cars" className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search cars..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-48">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Cars</SelectItem>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="rented">Rented</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={() => router.push("/agency/dashboard/add-car")} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Car
                </Button>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredCars.map((car) => (
                  <Card key={car.id} className="group hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" className="font-mono text-xs">
                            {car.id}
                          </Badge>
                          <div className="w-4 h-4 rounded-full border-2 border-gray-300" style={{ backgroundColor: car.color.toLowerCase() }}></div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => router.push(`/agency/dashboard/edit-car/${car.id}`)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity text-red-600 hover:text-red-700">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <CardTitle className="text-xl">{car.make} {car.model}</CardTitle>
                      <CardDescription className="flex items-center space-x-2">
                        <span>{car.year}</span>
                        <span>•</span>
                        <span className="capitalize">{car.transmission}</span>
                        <span>•</span>
                        <span>{car.mileage.toLocaleString()} km</span>
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-muted-foreground">Daily Rate</p>
                          <p className="font-semibold">₪{car.price}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Deposit</p>
                          <p className="font-semibold">₪{car.deposit.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Insurance</p>
                          <p className="font-semibold capitalize">{car.insuranceType}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Color</p>
                          <p className="font-semibold capitalize">{car.color}</p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-0">
                      <div className="flex w-full space-x-2">
                        <Button
                          variant="outline"
                          className="flex-1"
                          size="sm"
                          onClick={() => router.push(`/agency/dashboard/edit-car/${car.id}`)}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Button>
                        <Button variant="outline" className="flex-1" size="sm">
                          <Info className="mr-2 h-4 w-4" />
                          Details
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Bookings Tab */}
            <TabsContent value="bookings" className="space-y-4 sm:space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:space-x-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search bookings..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-full sm:w-64"
                    />
                  </div>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-full sm:w-48">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Bookings</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleCreateBooking} className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-sm sm:text-base">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Booking
                </Button>
              </div>

              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5" />
                    <span>Recent Bookings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-xs sm:text-sm">Booking ID</TableHead>
                        <TableHead className="text-xs sm:text-sm">Customer</TableHead>
                        <TableHead className="text-xs sm:text-sm">Car</TableHead>
                        <TableHead className="text-xs sm:text-sm">Dates</TableHead>
                        <TableHead className="text-xs sm:text-sm">Status</TableHead>
                        <TableHead className="text-xs sm:text-sm">Total</TableHead>
                        <TableHead className="text-xs sm:text-sm">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBookings.slice(0, 10).map((booking) => (
                        <TableRow key={booking.id} className="hover:bg-gray-50 transition-colors">
                          <TableCell className="font-mono text-xs sm:text-sm">
                            <Badge variant="outline">{booking.id}</Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-6 w-6 sm:h-8 sm:w-8">
                                <AvatarImage src="/placeholder.svg?height=32&width=32" />
                                <AvatarFallback className="text-xs">U</AvatarFallback>
                              </Avatar>
                              <span className="text-xs sm:text-sm">{booking.customerName || `User ${booking.id.split('-')[1]}`}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Car className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
                              <span className="text-xs sm:text-sm">Car {booking.carId.split('-')[3]}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-xs sm:text-sm">
                              <div>{new Date(booking.startDate).toLocaleDateString()}</div>
                              <div className="text-muted-foreground">to {new Date(booking.endDate).toLocaleDateString()}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={booking.status === "accepted" ? "default" : booking.status === "pending" ? "secondary" : "destructive"}
                              className="capitalize text-xs"
                            >
                              {booking.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-semibold text-xs sm:text-sm">MAD 2,250</TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewBookingDetails(booking)}
                                className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                              >
                                <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              {booking.status === "pending" && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50 h-6 w-6 sm:h-8 sm:w-8 p-0"
                                    onClick={() => handleBookingAction(booking.id, "accept")}
                                  >
                                    <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 h-6 w-6 sm:h-8 sm:w-8 p-0"
                                    onClick={() => handleBookingAction(booking.id, "reject")}
                                  >
                                    <X className="h-3 w-3 sm:h-4 sm:w-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Reviews Tab */}
            <TabsContent value="reviews" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {reviews.map((review) => (
                  <Card key={review.id} className="group hover:shadow-xl transition-all duration-300 transform hover:scale-105 border-0 bg-gradient-to-br from-white to-gray-50">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={review.customerAvatar} />
                            <AvatarFallback>{review.customerName.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <CardTitle className="text-lg">{review.customerName}</CardTitle>
                            <div className="flex items-center space-x-1">
                              {[...Array(5)].map((_, i) => (
                                <Star key={i} className={`h-4 w-4 ${i < review.rating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
                              ))}
                            </div>
                          </div>
                        </div>
                        <Badge
                          variant={review.status === "approved" ? "default" : review.status === "pending" ? "secondary" : "destructive"}
                          className="capitalize"
                        >
                          {review.status}
                        </Badge>
                      </div>
                      <CardDescription className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{review.date}</span>
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 leading-relaxed">{review.comment}</p>
                    </CardContent>
                    {review.status === "pending" && (
                      <CardFooter className="pt-0">
                        <div className="flex w-full space-x-2">
                          <Button
                            variant="outline"
                            className="flex-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                            size="sm"
                            onClick={() => handleReviewAction(review.id, "approve")}
                          >
                            <ThumbsUp className="mr-2 h-4 w-4" />
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                            size="sm"
                            onClick={() => handleReviewAction(review.id, "reject")}
                          >
                            <ThumbsDown className="mr-2 h-4 w-4" />
                            Reject
                          </Button>
                        </div>
                      </CardFooter>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* GPS Tracking Tab */}
            <TabsContent value="gps" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <MapPin className="h-5 w-5" />
                    <span>GPS Fleet Tracking</span>
                  </CardTitle>
                  <CardDescription>
                    Track your vehicles in real-time and monitor their locations across Morocco
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Car Selection */}
                  <div className="space-y-4">
                    <Label htmlFor="car-select-gps">Select Vehicle to Track</Label>
                    <Select
                      value={selectedCarForGPS}
                      onValueChange={(value) => {
                        setSelectedCarForGPS(value)
                        const selectedCar = cars.find(car => car.id === value)
                        if (selectedCar?.location) {
                          setMapCenter(selectedCar.location)
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Choose a vehicle from your fleet" />
                      </SelectTrigger>
                      <SelectContent>
                        {cars.map((car) => (
                          <SelectItem key={car.id} value={car.id}>
                            <div className="flex items-center space-x-2">
                              <Car className="h-4 w-4" />
                              <span>{car.make} {car.model} ({car.year}) - {car.color}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Map Container */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Vehicle Location</Label>
                      {selectedCarForGPS && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                          Live Tracking
                        </Badge>
                      )}
                    </div>

                    <div className="relative h-96 w-full bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 overflow-hidden">
                      {selectedCarForGPS ? (
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100">
                          {/* Map Placeholder with Car Location */}
                          <div className="relative w-full h-full">
                            {/* Morocco Map Background */}
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="text-center space-y-4">
                                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto animate-pulse">
                                  <Car className="h-8 w-8 text-white" />
                                </div>
                                <div className="space-y-2">
                                  <p className="text-lg font-semibold text-gray-800">
                                    {cars.find(car => car.id === selectedCarForGPS)?.make} {cars.find(car => car.id === selectedCarForGPS)?.model}
                                  </p>
                                  <p className="text-sm text-gray-600">
                                    {cars.find(car => car.id === selectedCarForGPS)?.location?.address}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    Last updated: {new Date().toLocaleTimeString()}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* Location Pins */}
                            {cars.map((car) => (
                              <div
                                key={car.id}
                                className={`absolute w-4 h-4 rounded-full border-2 ${car.id === selectedCarForGPS
                                  ? 'bg-red-500 border-white shadow-lg'
                                  : 'bg-gray-400 border-white'
                                  }`}
                                style={{
                                  left: `${((car.location?.lng || 0) + 10) * 2}%`,
                                  top: `${((car.location?.lat || 0) - 20) * 2}%`,
                                }}
                                title={`${car.make} ${car.model} - ${car.location?.address}`}
                              >
                                {car.id === selectedCarForGPS && (
                                  <div className="absolute -top-8 -left-2 bg-red-500 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                                    {car.make} {car.model}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center space-y-4">
                            <MapPin className="h-16 w-16 text-gray-400 mx-auto" />
                            <div className="space-y-2">
                              <p className="text-lg font-semibold text-gray-600">Select a Vehicle</p>
                              <p className="text-sm text-gray-500">
                                Choose a vehicle from the dropdown above to view its location
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Vehicle Details */}
                    {selectedCarForGPS && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Vehicle Info</p>
                          <p className="text-lg font-semibold">
                            {cars.find(car => car.id === selectedCarForGPS)?.make} {cars.find(car => car.id === selectedCarForGPS)?.model}
                          </p>
                          <p className="text-sm text-gray-500">
                            ID: {selectedCarForGPS}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Current Location</p>
                          <p className="text-lg font-semibold">
                            {cars.find(car => car.id === selectedCarForGPS)?.location?.address}
                          </p>
                          <p className="text-sm text-gray-500">
                            GPS: {cars.find(car => car.id === selectedCarForGPS)?.location?.lat}, {cars.find(car => car.id === selectedCarForGPS)?.location?.lng}
                          </p>
                        </div>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-600">Status</p>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            <span className="text-green-600 font-medium">Active</span>
                          </div>
                          <p className="text-sm text-gray-500">
                            Last signal: {new Date().toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Fleet Overview */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Fleet Overview</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {cars.map((car) => (
                          <div
                            key={car.id}
                            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${selectedCarForGPS === car.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                              }`}
                            onClick={() => {
                              setSelectedCarForGPS(car.id)
                              if (car.location) {
                                setMapCenter(car.location)
                              }
                            }}
                          >
                            <div className="flex items-center space-x-3">
                              <div className={`w-3 h-3 rounded-full ${selectedCarForGPS === car.id ? 'bg-blue-500' : 'bg-gray-400'
                                }`}></div>
                              <div className="flex-1">
                                <p className="font-medium">{car.make} {car.model}</p>
                                <p className="text-sm text-gray-500">{car.location?.address}</p>
                              </div>
                              <Car className="h-4 w-4 text-gray-400" />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Coupons Tab */}
            <TabsContent value="coupons" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5" />
                    <span>Manage Coupons</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CouponManager />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>Agency Settings</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Agency Avatar Upload */}
                  <div className="space-y-4">
                    <Label>Agency Logo/Avatar</Label>
                    <div className="flex items-center gap-4">
                      <Avatar className="h-20 w-20 border-2 border-gray-200">
                        <AvatarImage src="/placeholder.svg?height=80&width=80" alt="Agency Logo" />
                        <AvatarFallback className="text-lg font-semibold">PAR</AvatarFallback>
                      </Avatar>
                      <div className="space-y-2">
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          id="avatar-upload"
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('avatar-upload')?.click()}
                          className="flex items-center gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Upload New Logo
                        </Button>
                        <p className="text-xs text-muted-foreground">
                          Recommended: 200x200px, PNG or JPG format
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="agency-name">Agency Name</Label>
                      <Input
                        id="agency-name"
                        value={agencyName}
                        onChange={(e) => setAgencyName(e.target.value)}
                        placeholder="Enter your agency name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="owner-name">Owner Name</Label>
                      <Input
                        id="owner-name"
                        defaultValue={localStorage.getItem("ownerName") || "John Doe"}
                        disabled
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="agency-email">Email</Label>
                      <Input id="agency-email" type="email" defaultValue="<EMAIL>" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="agency-phone">Phone</Label>
                      <Input id="agency-phone" defaultValue="+212 6 12 34 56 78" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="agency-location">Location</Label>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="all-over-morocco"
                            checked={selectedCities.includes("all-over-morocco")}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCities(["all-over-morocco"])
                              } else {
                                setSelectedCities([])
                              }
                            }}
                          />
                          <Label htmlFor="all-over-morocco" className="font-medium">All Over Morocco</Label>
                        </div>

                        {!selectedCities.includes("all-over-morocco") && (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto border rounded-md p-3">
                            {moroccanCities.map((city) => (
                              <div key={city} className="flex items-center space-x-2">
                                <Checkbox
                                  id={city.toLowerCase()}
                                  checked={selectedCities.includes(city.toLowerCase())}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedCities([...selectedCities, city.toLowerCase()])
                                    } else {
                                      setSelectedCities(selectedCities.filter(c => c !== city.toLowerCase()))
                                    }
                                  }}
                                />
                                <Label htmlFor={city.toLowerCase()} className="text-sm">{city}</Label>
                              </div>
                            ))}
                          </div>
                        )}

                        {selectedCities.length > 0 && (
                          <div className="text-sm text-muted-foreground">
                            Selected: {selectedCities.includes("all-over-morocco")
                              ? "All Over Morocco"
                              : selectedCities.map(city => moroccanCities.find(c => c.toLowerCase() === city) || city).join(", ")
                            }
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="agency-description">Description</Label>
                    <Textarea
                      id="agency-description"
                      defaultValue="Premium Auto Rentals is a leading car rental agency in Morocco, offering a wide selection of vehicles for all your travel needs."
                      rows={4}
                    />
                  </div>

                  {/* Rental Policy */}
                  <div className="space-y-4">
                    <Label>Rental Policy</Label>
                    <div className="space-y-4">
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="min-age">Minimum Driver Age</Label>
                          <Input id="min-age" type="number" defaultValue="21" min="18" max="25" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="min-rental">Minimum Rental Period (days)</Label>
                          <Input id="min-rental" type="number" defaultValue="1" min="1" max="30" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cancellation-hours">Free Cancellation (hours before pickup)</Label>
                          <Input id="cancellation-hours" type="number" defaultValue="48" min="0" max="168" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="fuel-policy">Fuel Policy</Label>
                          <Select defaultValue="full-to-full">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="full-to-full">Full to Full</SelectItem>
                              <SelectItem value="full-to-empty">Full to Empty</SelectItem>
                              <SelectItem value="prepaid">Prepaid Fuel</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="rental-terms">Additional Terms & Conditions</Label>
                        <Textarea
                          id="rental-terms"
                          placeholder="Enter your specific rental terms and conditions..."
                          rows={6}
                          defaultValue="• Drivers must be 21+ years old with valid driver's license
• Security deposit required (varies by vehicle)
• Vehicle must be returned in the same condition as received
• Fuel tank must be returned full
• Late returns may incur additional charges
• Free cancellation up to 48 hours before pickup
• All cars delivered with full tank and should be returned the same"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Social Media Links */}
                  <div className="space-y-4">
                    <Label>Social Media Links</Label>
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <Label htmlFor="whatsapp-link" className="flex items-center gap-2">
                          <MessageCircle className="h-4 w-4 text-green-600" />
                          WhatsApp
                        </Label>
                        <Input
                          id="whatsapp-link"
                          placeholder="+212 6 12 34 56 78"
                          defaultValue="+212 6 12 34 56 78"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="instagram-link" className="flex items-center gap-2">
                          <Instagram className="h-4 w-4 text-pink-600" />
                          Instagram
                        </Label>
                        <Input
                          id="instagram-link"
                          placeholder="@premium_auto_rentals"
                          defaultValue="@premium_auto_rentals"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="facebook-link" className="flex items-center gap-2">
                          <Facebook className="h-4 w-4 text-blue-600" />
                          Facebook
                        </Label>
                        <Input
                          id="facebook-link"
                          placeholder="Premium Auto Rentals Morocco"
                          defaultValue="Premium Auto Rentals Morocco"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contract Upload */}
                  <div className="space-y-4">
                    <Label>Agency Contract</Label>
                    <div className="space-y-2">
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        className="hidden"
                        id="contract-upload"
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          if (file) {
                            const url = URL.createObjectURL(file)
                            localStorage.setItem("agencyContractUrl", url)
                            toast.success("Contract uploaded successfully!")
                          }
                        }}
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('contract-upload')?.click()}
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        Upload Contract
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Upload your agency contract (PDF, DOC, DOCX). This will be available to customers during booking.
                      </p>
                      {localStorage.getItem("agencyContractUrl") && (
                        <div className="flex items-center gap-2 p-2 bg-green-50 rounded border border-green-200">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-green-800">Contract uploaded</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              localStorage.removeItem("agencyContractUrl")
                              toast.success("Contract removed")
                            }}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label>Business Hours</Label>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>Opening Time</Label>
                        <Select defaultValue="08:00">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>Closing Time</Label>
                        <Select defaultValue="20:00">
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Label>Notifications</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="email-notifications" defaultChecked />
                        <Label htmlFor="email-notifications">Email notifications for new bookings</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="sms-notifications" />
                        <Label htmlFor="sms-notifications">SMS notifications for urgent matters</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="review-notifications" defaultChecked />
                        <Label htmlFor="review-notifications">Notifications for new reviews</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSaveSettings} className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">
                    <Shield className="mr-2 h-4 w-4" />
                    Save Settings
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Success Modal */}
        <SuccessModal />

        {/* Create Booking Modal */}
        <Dialog open={showCreateBookingModal} onOpenChange={setShowCreateBookingModal}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Booking
              </DialogTitle>
              <DialogDescription>
                Fill in the customer and booking details below
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Customer Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="customer-name">Customer Name *</Label>
                    <Input
                      id="customer-name"
                      value={newBooking.customerName}
                      onChange={(e) => setNewBooking({ ...newBooking, customerName: e.target.value })}
                      placeholder="Enter customer name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="customer-phone">Phone Number</Label>
                    <Input
                      id="customer-phone"
                      value={newBooking.customerPhone}
                      onChange={(e) => setNewBooking({ ...newBooking, customerPhone: e.target.value })}
                      placeholder="+212 6 12 34 56 78"
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="customer-email">Email Address</Label>
                    <Input
                      id="customer-email"
                      type="email"
                      value={newBooking.customerEmail}
                      onChange={(e) => setNewBooking({ ...newBooking, customerEmail: e.target.value })}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>

              {/* Booking Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Booking Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="car-select">Select Car *</Label>
                    <Select value={newBooking.carId} onValueChange={(value) => setNewBooking({ ...newBooking, carId: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a car" />
                      </SelectTrigger>
                      <SelectContent>
                        {cars.map((car) => (
                          <SelectItem key={car.id} value={car.id}>
                            {car.make} {car.model} ({car.year}) - MAD {car.price}/day
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="total-amount">Total Amount (MAD)</Label>
                    <Input
                      id="total-amount"
                      type="number"
                      value={newBooking.totalAmount}
                      onChange={(e) => setNewBooking({ ...newBooking, totalAmount: Number(e.target.value) })}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="start-date">Start Date *</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={newBooking.startDate}
                      onChange={(e) => setNewBooking({ ...newBooking, startDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="end-date">End Date *</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={newBooking.endDate}
                      onChange={(e) => setNewBooking({ ...newBooking, endDate: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pickup-location">Pickup Location</Label>
                    <Select value={newBooking.pickupLocation} onValueChange={(value) => setNewBooking({ ...newBooking, pickupLocation: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pickup location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map((city) => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dropoff-location">Dropoff Location</Label>
                    <Select value={newBooking.dropoffLocation} onValueChange={(value) => setNewBooking({ ...newBooking, dropoffLocation: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select dropoff location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map((city) => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Additional Notes */}
              <div className="space-y-2">
                <Label htmlFor="booking-notes">Additional Notes</Label>
                <Textarea
                  id="booking-notes"
                  value={newBooking.notes}
                  onChange={(e) => setNewBooking({ ...newBooking, notes: e.target.value })}
                  placeholder="Any special requirements or notes..."
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowCreateBookingModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitBooking}
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Booking
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Booking Details Modal */}
        {selectedBooking && (
          <Dialog open={bookingDetailsModalOpen} onOpenChange={setBookingDetailsModalOpen}>
            <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Booking Details
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Booking Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Booking Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="booking-id">Booking ID</Label>
                      <p>{selectedBooking.id}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="customer-name">Customer Name</Label>
                      <p>{selectedBooking.customerName}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="car-id">Car ID</Label>
                      <p>{selectedBooking.carId}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Start Date</Label>
                      <p>{new Date(selectedBooking.startDate).toLocaleDateString()}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-date">End Date</Label>
                      <p>{new Date(selectedBooking.endDate).toLocaleDateString()}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <p>{selectedBooking.status}</p>
                    </div>
                  </div>
                </div>

                {/* Additional Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Additional Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="total-amount">Total Amount</Label>
                      <p>MAD {selectedBooking.totalAmount?.toLocaleString() || "2,250"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pickup-location">Pickup Location</Label>
                      <p>{selectedBooking.pickupLocation || "Agadir International Airport"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="dropoff-location">Dropoff Location</Label>
                      <p>{selectedBooking.dropoffLocation || "Agadir International Airport"}</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notes">Notes</Label>
                      <p>{selectedBooking.notes || "No additional notes"}</p>
                    </div>
                  </div>
                </div>

                {/* Images Section */}
                {selectedBooking.images && selectedBooking.images.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Booking Images</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {selectedBooking.images.map((image, index) => (
                        <div
                          key={index}
                          className="aspect-square rounded-lg overflow-hidden border cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => handleImageClick(image)}
                        >
                          <img
                            src={image}
                            alt={`Booking image ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = "/placeholder.svg?height=200&width=200"
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <DialogFooter className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => setBookingDetailsModalOpen(false)}
                >
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Enlarged Image Modal */}
        {enlargedImage && (
          <Dialog open={!!enlargedImage} onOpenChange={() => setEnlargedImage(null)}>
            <DialogContent className="sm:max-w-[90vw] max-h-[90vh] p-0 bg-transparent border-none">
              <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={closeEnlargedImage}
                  className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white"
                >
                  <X className="h-4 w-4" />
                </Button>
                <img
                  src={enlargedImage}
                  alt="Enlarged booking image"
                  className="w-full h-full object-contain max-h-[80vh] rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.svg?height=400&width=600"
                  }}
                />
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DashboardLayout>
    </AuthGuard>
  )
}

function CouponManager() {
  const [coupons, setCoupons] = useState([
    { code: "ABC123", percent: 10, expiry: "2025-12-31" },
    { code: "SUMMER25", percent: 25, expiry: "2025-08-31" },
  ])
  const [newCode, setNewCode] = useState("")
  const [newPercent, setNewPercent] = useState("")
  const [newExpiry, setNewExpiry] = useState("")
  const [error, setError] = useState("")

  function handleAddCoupon(e: React.FormEvent) {
    e.preventDefault()
    if (!newCode || !newPercent || !newExpiry) {
      setError("All fields are required.")
      return
    }
    if (coupons.some(c => c.code === newCode)) {
      setError("Coupon code already exists.")
      return
    }
    setCoupons([
      ...coupons,
      { code: newCode.toUpperCase(), percent: Number(newPercent), expiry: newExpiry }
    ])
    setNewCode("")
    setNewPercent("")
    setNewExpiry("")
    setError("")
  }

  function handleDeleteCoupon(code: string) {
    setCoupons(coupons.filter(c => c.code !== code))
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleAddCoupon} className="flex flex-col md:flex-row gap-4 items-end">
        <div className="flex-1">
          <Label htmlFor="coupon-code">Code</Label>
          <Input id="coupon-code" value={newCode} onChange={e => setNewCode(e.target.value.toUpperCase())} placeholder="e.g. SPRING10" required />
        </div>
        <div className="flex-1">
          <Label htmlFor="coupon-percent">Percent</Label>
          <Input id="coupon-percent" type="number" min="1" max="100" value={newPercent} onChange={e => setNewPercent(e.target.value)} placeholder="e.g. 10" required />
        </div>
        <div className="flex-1">
          <Label htmlFor="coupon-expiry">Expiry</Label>
          <Input id="coupon-expiry" type="date" value={newExpiry} onChange={e => setNewExpiry(e.target.value)} required />
        </div>
        <Button type="submit" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800">Add Coupon</Button>
      </form>
      {error && <div className="text-red-500 text-sm">{error}</div>}
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Code</TableHead>
            <TableHead>Percent</TableHead>
            <TableHead>Expiry</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {coupons.map(coupon => (
            <TableRow key={coupon.code}>
              <TableCell className="font-mono">{coupon.code}</TableCell>
              <TableCell>{coupon.percent}%</TableCell>
              <TableCell>{coupon.expiry}</TableCell>
              <TableCell>
                <Button variant="outline" size="sm" onClick={() => handleDeleteCoupon(coupon.code)} className="text-red-600 hover:text-red-700">Delete</Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
