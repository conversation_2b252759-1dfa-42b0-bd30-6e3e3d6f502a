"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Car, Globe, DollarSign, ChevronDown, Check, User, Settings, Bell, LogOut, Calendar } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { useAuth } from "@/contexts/auth-context"
import { useRouter } from "next/navigation"
// Add the import for MobileNav at the top of the file
import { MobileNav } from "@/components/layout/mobile-nav"
import { useI18n, languages, type LanguageCode } from "@/i18n/i18n-provider"
import { useCurrency } from "@/contexts/currency-context"

type CurrencyInfo = {
  symbol: string
  name: string
}

const currencyInfo: Record<string, CurrencyInfo> = {
  MAD: { symbol: "د.م.", name: "Moroccan Dirham" },
  USD: { symbol: "$", name: "US Dollar" },
  EUR: { symbol: "€", name: "Euro" },
  GBP: { symbol: "£", name: "British Pound" },
}

// Update the MainNavbar component to include MobileNav
export function MainNavbar() {
  const router = useRouter()
  const { currency, setCurrency } = useCurrency()
  // const [language, setLanguage] = useState<Language>(languages[0])
  const { t, language: currentLanguage, changeLanguage } = useI18n()
  const { user, logout, isAgency, isAdmin } = useAuth()
  const [isScrolled, setIsScrolled] = useState(false)

  // Add scroll event listener to change navbar appearance on scroll
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true)
      } else {
        setIsScrolled(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        isScrolled
          ? "bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/80 border-b shadow-sm"
          : "bg-transparent border-transparent",
      )}
    >
      <div className="container flex h-16 items-center">
        <Link className="flex items-center justify-center mr-6 group" href="/">
          <div className="bg-primary/10 p-2 rounded-full transition-all duration-300 group-hover:bg-primary/20">
            <Car className="h-6 w-6 text-primary" />
          </div>
          <span className="ml-2 text-xl font-bold">morentcar</span>
        </Link>

        <nav className="hidden md:flex items-center gap-8 text-lg font-semibold">
          {[
            { href: "/listings", label: t("navbar.browseCars") },
            { href: "/about", label: t("navbar.aboutUs") },
            { href: "/contact", label: t("navbar.contactUs") },
            ...(isAgency ? [{ href: "/agency/dashboard", label: t("navbar.agencyDashboard") }] : []),
          ].map((item) => (
            <Link
              key={item.href}
              className="relative font-semibold transition-colors hover:text-primary group text-lg px-2 py-1 rounded-md hover:bg-primary/10"
              href={item.href}
            >
              {item.label}
              <span className="absolute inset-x-0 -bottom-1 h-0.5 bg-gradient-to-r from-primary to-secondary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
            </Link>
          ))}
        </nav>

        <div className="flex-1"></div>

        <div className="flex items-center gap-3">
          {/* Currency Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="hidden md:flex h-9 gap-1 border-transparent bg-muted/30"
              >
                <DollarSign className="h-3.5 w-3.5" />
                <span>{currency}</span>
                <ChevronDown className="h-3.5 w-3.5 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuRadioGroup
                value={currency}
                onValueChange={(value) => setCurrency(value as "MAD" | "USD" | "EUR" | "GBP")}
              >
                {Object.entries(currencyInfo).map(([code, info]) => (
                  <DropdownMenuRadioItem key={code} value={code} className="cursor-pointer">
                    <div className="flex items-center justify-between w-full">
                      <span>
                        {info.symbol} {code}
                      </span>
                      <span className="text-xs text-muted-foreground">{info.name}</span>
                    </div>
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Language Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="hidden md:flex h-9 gap-1 border-transparent bg-muted/30"
              >
                <Globe className="h-3.5 w-3.5" />
                <span>{languages[currentLanguage as LanguageCode].flag}</span>
                <ChevronDown className="h-3.5 w-3.5 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
              {(Object.keys(languages) as LanguageCode[]).map((langCode) => (
                <DropdownMenuItem
                  key={langCode}
                  className={cn(
                    "cursor-pointer flex items-center justify-between",
                    currentLanguage === langCode && "font-medium",
                  )}
                  onClick={() => changeLanguage(langCode)}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-base">{languages[langCode].flag}</span>
                    <span>{languages[langCode].name}</span>
                  </div>
                  {currentLanguage === langCode && <Check className="h-4 w-4" />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Authentication UI */}
          <div className="hidden md:flex items-center gap-2 ml-2">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="relative h-9 w-9 rounded-full overflow-hidden ring-2 ring-background transition-all duration-200 hover:ring-primary/20"
                  >
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} alt={`${user.firstName} ${user.lastName}`} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white">
                        {user.firstName ? user.firstName.charAt(0) : "U"}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="flex items-center justify-start gap-2 p-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.avatar || "/placeholder.svg"} alt={`${user.firstName} ${user.lastName}`} />
                      <AvatarFallback className="bg-gradient-to-br from-primary to-secondary text-white">
                        {user.firstName ? user.firstName.charAt(0) : "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col space-y-0.5 leading-none">
                      <p className="font-medium">{user.firstName} {user.lastName}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />

                  {isAgency ? (
                    // Agency-specific menu items
                    <>
                      <DropdownMenuItem asChild>
                        <Link href="/agency/dashboard" className="cursor-pointer flex items-center">
                          <Car className="mr-2 h-4 w-4" />
                          <span>{t("navbar.agencyDashboard")}</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/agency/dashboard?tab=bookings" className="cursor-pointer flex items-center">
                          <Calendar className="mr-2 h-4 w-4" />
                          <span>{t("navbar.bookings")}</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/agency/dashboard?tab=settings" className="cursor-pointer flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>{t("navbar.settings")}</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  ) : (
                    // Regular user menu items
                    <>
                      <DropdownMenuItem asChild>
                        <Link href="/user/dashboard" className="cursor-pointer flex items-center">
                          <User className="mr-2 h-4 w-4" />
                          <span>{t("navbar.profile")}</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/user/settings" className="cursor-pointer flex items-center">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>{t("navbar.settings")}</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/user/notifications" className="cursor-pointer flex items-center">
                          <Bell className="mr-2 h-4 w-4" />
                          <span>{t("navbar.notifications")}</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="cursor-pointer flex items-center text-red-500 focus:text-red-500"
                    onClick={logout}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>{t("navbar.logout")}</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/auth")}
                  className="h-9 hover:bg-accent hover:text-accent-foreground transition-all duration-200"
                >
                  {t("navbar.logIn")}
                </Button>
                <Button
                  size="sm"
                  onClick={() => router.push("/auth?tab=signup")}
                  className="h-9 bg-gradient-to-r from-primary to-secondary hover:opacity-90 transition-all duration-200"
                >
                  {t("navbar.signUp")}
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu */}
          <MobileNav />
        </div>
      </div>
    </header>
  )
}
