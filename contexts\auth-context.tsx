"use client"

import { createContext, useContext, useState, useEffect, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { supabase } from '@/lib/supabase/client'
import { SupabaseAuthService } from '@/services/supabase-auth.service'
import { User as SupabaseUser, Session } from '@supabase/supabase-js'
import { Database } from '@/types/supabase'

type UserProfile = Database['public']['Tables']['users']['Row']

interface AuthContextType {
  user: (SupabaseUser & UserProfile) | null
  session: Session | null
  userProfile: UserProfile | null
  login: (email: string, password: string) => Promise<{ error: any }>
  signUp: (email: string, password: string, userData?: Partial<UserProfile>) => Promise<{ error: any }>
  logout: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>
  isAuthenticated: boolean
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<(SupabaseUser & UserProfile) | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()

  // Helper to fetch user profile from public.users
  const fetchUserProfile = async (userId: string) => {
    const { data, error } = await SupabaseAuthService.getUserProfile(userId)
    if (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
    return data
  }

  useEffect(() => {
    const getInitialSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      if (session?.user) {
        const profile = await fetchUserProfile(session.user.id)
        if (profile) {
          setUser({ ...session.user, ...profile })
          setUserProfile(profile)
          setIsAuthenticated(true)
        } else {
          setUser(null)
          setUserProfile(null)
          setIsAuthenticated(false)
        }
      } else {
        setUser(null)
        setUserProfile(null)
        setIsAuthenticated(false)
      }
      setIsLoading(false)
    }
    getInitialSession()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session)
        if (session?.user) {
          const profile = await fetchUserProfile(session.user.id)
          if (profile) {
            setUser({ ...session.user, ...profile })
            setUserProfile(profile)
            setIsAuthenticated(true)
          } else {
            setUser(null)
            setUserProfile(null)
            setIsAuthenticated(false)
          }
        } else {
          setUser(null)
          setUserProfile(null)
          setIsAuthenticated(false)
        }
        setIsLoading(false)
      }
    )
    return () => subscription.unsubscribe()
  }, [])

  const login = async (email: string, password: string) => {
    setIsLoading(true)
    try {
      const { data, error } = await SupabaseAuthService.signIn(email, password)

      if (error) {
        return { error }
      }

      // Fetch and set user profile after login
      if (data.user) {
        const profile = await fetchUserProfile(data.user.id)
        if (profile) {
          setUser({ ...data.user, ...profile })
          setUserProfile(profile)
          setIsAuthenticated(true)
        }
      }

      // Check if user is admin (you can customize this logic)
      if (email === "<EMAIL>") {
        localStorage.setItem("adminAuthenticated", "true")
        document.cookie = "adminAuthenticated=true; path=/; max-age=86400"
      }

      return { error: null }
    } catch (error) {
      console.error("Login failed", error)
      return { error }
    } finally {
      setIsLoading(false)
    }
  }

  const signUp = async (email: string, password: string, userData?: Partial<UserProfile>) => {
    setIsLoading(true)
    try {
      const { data, error } = await SupabaseAuthService.signUp(email, password, userData || {})

      if (error) {
        return { error }
      }

      return { error: null }
    } catch (error) {
      console.error("Sign up failed", error)
      return { error }
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      await SupabaseAuthService.signOut()
      localStorage.removeItem("adminAuthenticated")
      document.cookie = "adminAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
      setUser(null)
      setSession(null)
      setUserProfile(null)
      setIsAuthenticated(false)
      router.push("/")
    } catch (error) {
      console.error("Logout failed", error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { error: new Error('No user logged in') }

    setIsLoading(true)
    try {
      const { data, error } = await SupabaseAuthService.updateUserProfile(user.id, updates)

      if (error) {
        return { error }
      }

      if (data) {
        setUser({ ...user, ...data })
        setUserProfile(data)
      }

      return { error: null }
    } catch (error) {
      console.error("Profile update failed", error)
      return { error }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthContext.Provider value={{
      user,
      session,
      userProfile,
      login,
      signUp,
      logout,
      updateProfile,
      isAuthenticated,
      isLoading
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}


