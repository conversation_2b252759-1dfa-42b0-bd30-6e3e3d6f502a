"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Upload, X, Shield, DollarSign } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "sonner"
import { AuthGuard } from "@/components/features/auth/auth-guard"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Image from 'next/image'

const carBrandsAZ = [
  "Audi", "BMW", "Chevrolet", "Citroen", "Dacia", "Fiat", "Ford", "Honda", "Hyundai", "Infiniti", "Jaguar", "Jeep", "Kia", "Land Rover", "Lexus", "Mazda", "Mercedes", "Mini", "Mitsubishi", "Nissan", "Opel", "Peugeot", "Porsche", "Renault", "Skoda", "Subaru", "Suzuki", "Toyota", "Volkswagen", "Volvo"
];

const moroccanCities = [
  "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla",
  "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune",
  "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi",
  "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
].sort((a, b) => a.localeCompare(b))

// Sample car data - this will be replaced with actual data fetching
const carData = {
  id: "CAR-AGY-001-001",
  make: "Toyota",
  model: "Corolla",
  year: 2022,
  transmission: "automatic",
  price: 750,
  pricePerDay: 750,
  pricePerWeek: 4500,
  pricePerMonth: 15000,
  mileage: 15000,
  insuranceType: "comprehensive",
  deposit: 5000,
  color: "Red",
  location: "Agadir",
  images: [
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
    "/placeholder.svg?height=400&width=600",
  ],
  description: "Premium sedan with excellent fuel efficiency and modern features.",
  available: true,
}

const availableFeatures = [
  { key: 'transmission', name: 'Transmission', icon: '/icons/gearshift-shift-svgrepo-com.svg', type: 'select', options: ['Automatic', 'Manual'] },
  { key: 'bodyType', name: 'Body Type', icon: '/icons/body-type.svg', type: 'select', options: ['SUV', 'Sedan', 'Coupe', 'Convertible', 'Hatchback', 'Wagon', 'Van', 'Truck'] },
  { key: 'year', name: 'Year Model', icon: '/icons/year.svg', type: 'select', options: Array.from({ length: 30 }, (_, i) => `${2024 - i}`) },
  { key: 'fuelType', name: 'Fuel Type', icon: '/icons/gas-svgrepo-com.svg', type: 'select', options: ['Diesel', 'Gasoline', 'Hybrid', 'Electric'] },
  { key: 'ac', name: 'AC', icon: '/icons/car-air-conditioning-svgrepo-com.svg', type: 'boolean' },
  { key: 'engineCapacity', name: 'Engine Capacity', icon: '/icons/car-engine-svgrepo-com.svg', type: 'input', placeholder: 'e.g. 1.6L' },
  { key: 'bluetooth', name: 'Bluetooth', icon: '/icons/bluetooth-on-svgrepo-com.svg', type: 'boolean' },
  { key: 'airbags', name: 'Airbags', icon: '/icons/car-seat-with-seatbelt-svgrepo-com.svg', type: 'boolean' },
  { key: 'gps', name: 'GPS', icon: '/icons/gps.svg', type: 'boolean' },
  { key: 'usbCharger', name: 'USB Charger', icon: '/icons/usb.svg', type: 'boolean' },
  { key: 'sunroof', name: 'Sunroof', icon: '/icons/sunroof.svg', type: 'boolean' },
];

export default function EditCarPage() {
  const params = useParams()
  const router = useRouter()
  const carId = params.id as string

  const [car, setCar] = useState(carData)
  const [carImages, setCarImages] = useState<string[]>(carData.images)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [features, setFeatures] = useState<Record<string, string | boolean>>({
    transmission: carData.transmission,
    year: carData.year.toString(),
    ac: true,
    engineCapacity: '',
    bluetooth: true,
    airbags: true,
    gps: false,
    usbCharger: true,
    sunroof: false,
  })

  // Function to fetch car data based on ID
  const fetchCarData = (id: string) => {
    // In a real app, this would be an API call
    // For now, we'll simulate fetching from localStorage or a mock database
    const mockCars = [
      {
        id: "CAR-AGY-001-001",
        make: "Toyota",
        model: "Corolla",
        year: 2022,
        transmission: "automatic",
        price: 750,
        pricePerDay: 750,
        pricePerWeek: 4500,
        pricePerMonth: 15000,
        mileage: 15000,
        insuranceType: "comprehensive",
        deposit: 5000,
        color: "Red",
        location: "Agadir",
        images: ["/placeholder.svg?height=400&width=600"],
        description: "Premium sedan with excellent fuel efficiency and modern features.",
        available: true,
      },
      {
        id: "CAR-AGY-001-002",
        make: "Honda",
        model: "Civic",
        year: 2023,
        transmission: "automatic",
        price: 800,
        pricePerDay: 800,
        pricePerWeek: 4800,
        pricePerMonth: 16000,
        mileage: 8000,
        insuranceType: "premium",
        deposit: 6000,
        color: "Blue",
        location: "Marrakech",
        images: ["/placeholder.svg?height=400&width=600"],
        description: "Modern compact car with advanced safety features.",
        available: true,
      },
      {
        id: "CAR-AGY-001-003",
        make: "Mercedes",
        model: "C-Class",
        year: 2021,
        transmission: "automatic",
        price: 1500,
        pricePerDay: 1500,
        pricePerWeek: 9000,
        pricePerMonth: 30000,
        mileage: 20000,
        insuranceType: "premium",
        deposit: 10000,
        color: "Black",
        location: "Rabat",
        images: ["/placeholder.svg?height=400&width=600"],
        description: "Luxury sedan with premium features and comfort.",
        available: true,
      },
    ]

    const foundCar = mockCars.find(c => c.id === id)
    if (foundCar) {
      setCar(foundCar)
      setCarImages(foundCar.images)
      setFeatures({
        transmission: foundCar.transmission,
        year: foundCar.year.toString(),
        ac: true,
        engineCapacity: '',
        bluetooth: true,
        airbags: true,
        gps: false,
        usbCharger: true,
        sunroof: false,
      })
    } else {
      toast.error("Car not found")
      router.push("/agency/dashboard?tab=cars")
    }
    setIsLoading(false)
  }

  useEffect(() => {
    if (carId) {
      fetchCarData(carId)
    }
  }, [carId])

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files).map((file) => URL.createObjectURL(file))
      setCarImages([...carImages, ...newImages])
    }
  }

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...carImages]
    updatedImages.splice(index, 1)
    setCarImages(updatedImages)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false)
      toast.success("Car updated successfully")
      // Redirect to dashboard
      router.push("/agency/dashboard?tab=cars")
    }, 1500)
  }

  return (
    <AuthGuard requiredRole="agency">
      <div className="container py-10">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">Edit Car</h1>
          <Button variant="outline" size="sm" asChild>
            <Link href="/agency/dashboard?tab=cars">Back to Dashboard</Link>
          </Button>
        </div>

        {isLoading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading car details...</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <form onSubmit={handleSubmit}>
            <Card>
              <CardHeader>
                <CardTitle>Car Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="make">Make</Label>
                    <Select
                      value={car.make}
                      onValueChange={(value) => setCar({ ...car, make: value })}
                      required
                    >
                      <SelectTrigger id="make">
                        <SelectValue placeholder="Select a make" />
                      </SelectTrigger>
                      <SelectContent>
                        {carBrandsAZ.map(brand => (
                          <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="model">Model</Label>
                    <Input
                      id="model"
                      placeholder="e.g. Corolla"
                      value={car.model}
                      onChange={(e) => setCar({ ...car, model: e.target.value })}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="color">Color</Label>
                    <div className="flex gap-2 flex-wrap">
                      {["White", "Black", "Silver", "Gray", "Red", "Blue", "Green", "Yellow", "Orange", "Purple"].map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded border-2 flex items-center justify-center focus:outline-none ${car.color === color ? 'border-blue-600 ring-2 ring-blue-300' : 'border-gray-300'}`}
                          style={{ backgroundColor: color.toLowerCase() }}
                          aria-label={color}
                          onClick={() => setCar({ ...car, color })}
                        >
                          {car.color === color && <span className="w-3 h-3 rounded-full bg-white border border-blue-600" />}
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="year">Year</Label>
                    <Select
                      value={car.year.toString()}
                      onValueChange={(value) => setCar({ ...car, year: parseInt(value) })}
                      required
                    >
                      <SelectTrigger id="year">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 30 }, (_, i) => `${2024 - i}`).map(year => (
                          <SelectItem key={year} value={year}>{year}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="mileage">Mileage (km)</Label>
                    <Input
                      id="mileage"
                      type="number"
                      placeholder="e.g. 15000"
                      value={car.mileage}
                      onChange={(e) => setCar({ ...car, mileage: Number.parseInt(e.target.value) })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="deposit">Security Deposit (MAD)</Label>
                    <Input
                      id="deposit"
                      type="number"
                      placeholder="e.g. 5000"
                      value={car.deposit}
                      onChange={(e) => setCar({ ...car, deposit: Number.parseInt(e.target.value) })}
                      required
                    />
                    <p className="text-sm text-muted-foreground">Amount required as security deposit from the renter</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Select
                      value={car.location}
                      onValueChange={(value) => setCar({ ...car, location: value })}
                      required
                    >
                      <SelectTrigger id="location">
                        <SelectValue placeholder="Select a location" />
                      </SelectTrigger>
                      <SelectContent>
                        {moroccanCities.map(city => (
                          <SelectItem key={city} value={city}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Pricing Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Pricing Options</h3>
                  </div>

                  <Card>
                    <CardContent className="pt-6">
                      <Tabs defaultValue="daily" className="w-full">
                        <TabsList className="grid grid-cols-3 mb-4">
                          <TabsTrigger value="daily">Daily Rate</TabsTrigger>
                          <TabsTrigger value="weekly">Weekly Rate</TabsTrigger>
                          <TabsTrigger value="monthly">Monthly Rate</TabsTrigger>
                        </TabsList>

                        <TabsContent value="daily">
                          <div className="space-y-2">
                            <Label htmlFor="daily-price">Daily Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="daily-price"
                                type="number"
                                placeholder="e.g. 750"
                                className="pl-10"
                                value={car.pricePerDay}
                                onChange={(e) => setCar({ ...car, pricePerDay: Number.parseInt(e.target.value) })}
                                required
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Standard daily rental rate</p>
                          </div>
                        </TabsContent>

                        <TabsContent value="weekly">
                          <div className="space-y-2">
                            <Label htmlFor="weekly-price">Weekly Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="weekly-price"
                                type="number"
                                placeholder="e.g. 4500"
                                className="pl-10"
                                value={car.pricePerWeek}
                                onChange={(e) => setCar({ ...car, pricePerWeek: Number.parseInt(e.target.value) })}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Discounted rate for 7+ day rentals</p>
                          </div>
                        </TabsContent>

                        <TabsContent value="monthly">
                          <div className="space-y-2">
                            <Label htmlFor="monthly-price">Monthly Price (MAD)</Label>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Input
                                id="monthly-price"
                                type="number"
                                placeholder="e.g. 15000"
                                className="pl-10"
                                value={car.pricePerMonth}
                                onChange={(e) => setCar({ ...car, pricePerMonth: Number.parseInt(e.target.value) })}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground">Discounted rate for 30+ day rentals</p>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </div>

                {/* Car Features Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">Car Features</h3>
                  </div>
                  {availableFeatures.map((feature, idx) => (
                    <div key={feature.key} className="flex items-center gap-4 mb-2">
                      <Image src={feature.icon} alt={feature.name} width={32} height={32} />
                      <span className="w-32">{feature.name}</span>
                      {feature.type === 'boolean' && (
                        <input
                          type="checkbox"
                          checked={!!features[feature.key]}
                          onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.checked }))}
                        />
                      )}
                      {feature.type === 'select' && (
                        <select
                          value={features[feature.key] as string || ''}
                          onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.value }))}
                          className="border rounded px-2 py-1"
                        >
                          <option value="">Select</option>
                          {feature.options?.map((opt: string) => <option key={opt} value={opt}>{opt}</option>)}
                        </select>
                      )}
                      {feature.type === 'input' && (
                        <input
                          type="text"
                          placeholder={feature.placeholder}
                          value={features[feature.key] as string || ''}
                          onChange={e => setFeatures(f => ({ ...f, [feature.key]: e.target.value }))}
                          className="border rounded px-2 py-1"
                        />
                      )}
                    </div>
                  ))}
                </div>

                {/* Insurance Options */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-primary" />
                    <Label className="text-base font-medium">Insurance Type</Label>
                  </div>

                  <RadioGroup
                    value={car.insuranceType}
                    onValueChange={(value) => setCar({ ...car, insuranceType: value })}
                    className="grid grid-cols-1 md:grid-cols-3 gap-4"
                  >
                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="basic" id="basic" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="basic" className="font-medium">
                          Basic Insurance
                        </Label>
                        <p className="text-sm text-muted-foreground">Covers third-party liability only</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="comprehensive" id="comprehensive" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="comprehensive" className="font-medium">
                          Comprehensive
                        </Label>
                        <p className="text-sm text-muted-foreground">Covers damage to the vehicle and third-party</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-2 border p-4 rounded-md hover:border-primary cursor-pointer">
                      <RadioGroupItem value="premium" id="premium" className="mt-1" />
                      <div className="grid gap-1">
                        <Label htmlFor="premium" className="font-medium">
                          Premium
                        </Label>
                        <p className="text-sm text-muted-foreground">Full coverage with zero deductible</p>
                      </div>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your car..."
                    className="min-h-[120px]"
                    value={car.description}
                    onChange={(e) => setCar({ ...car, description: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Car Images</Label>
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">Current Images ({carImages.length})</p>
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                      {carImages.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`Car image ${index + 1}`}
                            className="w-full h-24 object-cover rounded-md"
                          />
                          <button
                            type="button"
                            className="absolute top-1 right-1 bg-black/70 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleRemoveImage(index)}
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center mt-4">
                    <Upload className="h-10 w-10 text-muted-foreground mb-3" />
                    <p className="text-sm text-muted-foreground mb-1">Add more images (multiple allowed)</p>
                    <p className="text-xs text-muted-foreground mb-4">PNG, JPG (max. 5MB per image)</p>
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => document.getElementById("image-upload")?.click()}
                    >
                      Select Images
                    </Button>
                    <Input
                      id="image-upload"
                      type="file"
                      className="hidden"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" type="button" asChild>
                  <Link href="/agency/dashboard?tab=cars">Cancel</Link>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Saving Changes..." : "Save Changes"}
                </Button>
              </CardFooter>
            </Card>
          </form>
        )}
      </div>
    </AuthGuard>
  )
}
