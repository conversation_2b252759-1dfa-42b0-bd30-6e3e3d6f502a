"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { toast } from "sonner"
import { Sidebar } from "@/components/admin/sidebar"
import { SupabaseAuthService } from "@/services/supabase-auth.service"
import { useAuth } from "@/contexts/auth-context"
import { CurrencyProvider } from "@/contexts/currency-context"

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [isAuthed, setIsAuthed] = useState(false)
    const { user } = useAuth()

    useEffect(() => {
        const checkAdminAuth = async () => {
            try {
                // Check for admin cookie
                const cookies = document.cookie.split(';').map(c => c.trim())
                const adminCookie = cookies.find(c => c.startsWith('adminAuthenticated='))
                const isAdminAuthenticated = !!adminCookie && adminCookie.split('=')[1] === 'true'

                if (!isAdminAuthenticated) {
                    toast.error("Admin authentication required")
                    router.replace('/admin-login')
                    return
                }

                // Verify with Supabase session
                const { session } = await SupabaseAuthService.getSession()
                if (!session) {
                    // Clear admin cookie if no session
                    document.cookie = "adminAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
                    toast.error("Session expired. Please login again.")
                    router.replace('/admin-login')
                    return
                }

                // Verify admin role
                const { data: userProfile } = await SupabaseAuthService.getUserProfile(session.user.id)
                if (!userProfile || userProfile.role !== 'admin') {
                    // Clear admin cookie if not admin
                    document.cookie = "adminAuthenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT"
                    await SupabaseAuthService.signOut()
                    toast.error("Access denied. Admin privileges required.")
                    router.replace('/admin-login')
                    return
                }

                setIsAuthed(true)
            } catch (error) {
                console.error("Admin auth check failed:", error)
                toast.error("Authentication check failed")
                router.replace('/admin-login')
            } finally {
                setIsLoading(false)
            }
        }

        checkAdminAuth()
    }, [router])

    if (isLoading) return null
    if (!isAuthed) return null

    return (
        <CurrencyProvider>
            <div className="flex min-h-screen bg-gray-50">
                <Sidebar />
                <div className="flex-1">
                    {children}
                </div>
            </div>
        </CurrencyProvider>
    )
} 
