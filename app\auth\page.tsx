"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Car, Eye, EyeOff, Mail, Lock, User, Building, Loader2, CheckCircle, ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import { ForgotPasswordModal } from "@/components/features/auth/forgot-password-modal"
import { useI18n } from "@/i18n/i18n-provider"
import OfficeIcon from "public/icons/office.svg"
import UserIcon from "public/icons/user.svg"

export default function AuthPage() {
  const { t } = useI18n()
  const router = useRouter()
  const { login, signUp, logout, user, isAuthenticated, isLoading } = useAuth() // Added isLoading
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [forgotPasswordModalOpen, setForgotPasswordModalOpen] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [successMessage, setSuccessMessage] = useState("")
  const [activeTab, setActiveTab] = useState("login")

  // Login form state
  const [loginEmail, setLoginEmail] = useState("")
  const [loginPassword, setLoginPassword] = useState("")

  // Signup form state
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [agencyName, setAgencyName] = useState("")
  const [signupEmail, setSignupEmail] = useState("")
  const [signupPassword, setSignupPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [signupRole, setSignupRole] = useState<"user" | "agency">("user")
  const [agreeToTerms, setAgreeToTerms] = useState(false)

  useEffect(() => {
    if (!isLoading && user && isAuthenticated) {
      // Check if user is admin based on email
      const isAdmin = user.email === "<EMAIL>"
      if (isAdmin) {
        router.replace("/admin/dashboard")
      } else {
        const redirectPath = user.role === "agency" ? "/agency/dashboard" : "/user/dashboard"
        router.replace(redirectPath)
      }
    }
  }, [user, isAuthenticated, isLoading, router])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!loginEmail || !loginPassword) {
      toast.error(t("auth.fillAllFields"))
      return
    }
    try {
      const { error } = await login(loginEmail, loginPassword)

      if (error) {
        // Show specific error messages
        switch (error.message) {
          case "Invalid login credentials":
            toast.error(t("auth.invalidCredentials"))
            break
          // TEMPORARY: Disable email confirmation check for testing
          // case "Email not confirmed":
          //   toast.error("Please verify your email before logging in. Check your inbox for the verification link.")
          //   break
          default:
            toast.error(error.message || t("auth.loginError"))
        }
      } else {
        setSuccessMessage(`Login successful! Redirecting to dashboard...`)
        setShowSuccess(true)
        // Do NOT manually push here; let the useEffect handle it!
      }
    } catch (error) {
      toast.error(t("auth.loginError"))
    } finally {
      // setIsLoading(false) // This line is removed as per the edit hint
    }
  }

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!firstName || !lastName || !signupEmail || !signupPassword || !confirmPassword) {
      toast.error(t("auth.fillAllFields"))
      return
    }

    if (signupRole === "agency" && !agencyName.trim()) {
      toast.error("Agency name is required")
      return
    }

    if (signupPassword !== confirmPassword) {
      toast.error(t("auth.passwordsDoNotMatch"))
      return
    }

    if (signupPassword.length < 6) {
      toast.error(t("auth.passwordTooShort"))
      return
    }

    if (!agreeToTerms) {
      toast.error("Please agree to the terms and conditions")
      return
    }

    try {
      // Prepare user metadata
      const userData = {
        first_name: firstName,
        last_name: lastName,
        role: signupRole,
        agency_name: signupRole === "agency" ? agencyName : null
      }

      const { error } = await signUp(signupEmail, signupPassword, userData)

      if (error) {
        // Handle specific error types
        switch (error.message) {
          case "User already registered":
            toast.error(t("auth.emailAlreadyExists"))
            break
          case "Password should be at least 6 characters":
            toast.error(t("auth.passwordTooShort"))
            break
          // TEMPORARY: Disable email confirmation check for testing
          // case "Email not confirmed":
          //   toast.error("Please verify your email before logging in. Check your inbox for the verification link.")
          //   break
          default:
            toast.error(error.message || t("auth.signupError"))
        }
      } else {
        // Store agency name in localStorage if it's an agency signup
        if (signupRole === "agency" && agencyName) {
          localStorage.setItem("agencyName", agencyName)
          localStorage.setItem("ownerName", `${firstName} ${lastName}`)
        }

        // Force logout after signup (in case Supabase auto-logs in)
        await logout();

        // Show success and switch to login tab
        toast.success("Account created! Please log in.")
        setActiveTab("login")
      }
    } catch (error) {
      toast.error(t("auth.signupError"))
    } finally {
      // setIsLoading(false) // This line is removed as per the edit hint
    }
  }

  // Success overlay component
  const SuccessOverlay = () => (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-white rounded-2xl p-8 max-w-md mx-4 text-center shadow-2xl">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Success!</h3>
        <p className="text-gray-600 mb-6">{successMessage}</p>
        <div className="flex justify-center">
          <Loader2 className="w-6 h-6 text-primary animate-spin" />
        </div>
      </div>
    </div>
  )

  if (showSuccess) {
    return <SuccessOverlay />
  }

  // Show loading spinner while auth state is loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen">
      {/* Left column - Shadowed Image with Top Text */}
      <div className="hidden md:flex md:w-1/2 relative flex-col justify-start items-center text-white p-0 overflow-hidden">
        {/* Background image */}
        <img
          src="/images/image1.png"
          alt="Discover Morocco"
          className="absolute inset-0 w-full h-full object-cover"
          style={{ zIndex: 0 }}
        />
        {/* Top shadow overlay */}
        <div className="absolute top-0 left-0 w-full h-40 bg-gradient-to-b from-black/60 to-transparent" style={{ zIndex: 1 }} />
        {/* Top overlay text */}
        <div className="absolute top-8 left-0 w-full flex justify-center z-10">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center drop-shadow-lg px-4">
            Discover Morocco on wheels with the best cars available
          </h2>
        </div>
      </div>

      {/* Right column - Auth forms */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="login">{t("auth.logIn")}</TabsTrigger>
              <TabsTrigger value="signup">{t("auth.signUp")}</TabsTrigger>
            </TabsList>

            {/* Login Form */}
            <TabsContent value="login">
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold">{t("auth.welcomeBack")}</h2>
                  <p className="text-muted-foreground mt-2">{t("auth.loginSubtitle")}</p>
                </div>

                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-email">{t("auth.email")}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="login-email"
                        type="email"
                        placeholder={t("auth.emailPlaceholder")}
                        className="pl-10"
                        value={loginEmail}
                        onChange={(e) => setLoginEmail(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="login-password">{t("auth.password")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="login-password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t("auth.passwordPlaceholder")}
                        className="pl-10"
                        value={loginPassword}
                        onChange={(e) => setLoginPassword(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-muted-foreground"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="remember" disabled={isLoading} />
                      <Label htmlFor="remember" className="text-sm">
                        {t("auth.rememberMe")}
                      </Label>
                    </div>
                    <Button
                      variant="link"
                      className="p-0 h-auto text-sm"
                      onClick={() => setForgotPasswordModalOpen(true)}
                      disabled={isLoading}
                    >
                      {t("auth.forgotPassword")}
                    </Button>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-300 transform hover:scale-105"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("auth.loggingIn")}
                      </>
                    ) : (
                      <>
                        {t("auth.logIn")}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>

                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    {t("auth.dontHaveAccount")}{" "}
                    <Button
                      variant="link"
                      className="p-0 h-auto"
                      onClick={() => setActiveTab("signup")}
                      disabled={isLoading}
                    >
                      {t("auth.signUp")}
                    </Button>
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Signup Form */}
            <TabsContent value="signup">
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold">{t("auth.createAccount")}</h2>
                  <p className="text-muted-foreground mt-2">{t("auth.signupSubtitle")}</p>
                </div>

                <form onSubmit={handleSignup} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="first-name">{t("auth.firstName")}</Label>
                      <Input
                        id="first-name"
                        placeholder={t("auth.firstNamePlaceholder")}
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-name">{t("auth.lastName")}</Label>
                      <Input
                        id="last-name"
                        placeholder={t("auth.lastNamePlaceholder")}
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-email">{t("auth.email")}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="signup-email"
                        type="email"
                        placeholder={t("auth.emailPlaceholder")}
                        className="pl-10"
                        value={signupEmail}
                        onChange={(e) => setSignupEmail(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-password">{t("auth.password")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="signup-password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t("auth.passwordPlaceholder")}
                        className="pl-10"
                        value={signupPassword}
                        onChange={(e) => setSignupPassword(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-muted-foreground"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isLoading}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">{t("auth.confirmPassword")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="confirm-password"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t("auth.confirmPasswordPlaceholder")}
                        className="pl-10"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-muted-foreground"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={isLoading}
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>{t("auth.joinAs")}</Label>
                    <RadioGroup
                      defaultValue="user"
                      value={signupRole}
                      onValueChange={(value) => setSignupRole(value as "user" | "agency")}
                      className="flex gap-4"
                      disabled={isLoading}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="user" id="signup-user" />
                        <User className="h-5 w-5 text-primary" />
                        <Label htmlFor="signup-user" className="font-extrabold">{t("auth.traveler")}</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="agency" id="signup-agency" />
                        <Building className="h-5 w-5 text-primary" />
                        <Label htmlFor="signup-agency" className="font-extrabold">{t("auth.rentalAgency")}</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {signupRole === "agency" && (
                    <div className="space-y-2">
                      <Label htmlFor="agency-name">Agency Name *</Label>
                      <Input
                        id="agency-name"
                        placeholder="Enter your agency name"
                        value={agencyName}
                        onChange={(e) => setAgencyName(e.target.value)}
                        required
                        disabled={isLoading}
                      />
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      checked={agreeToTerms}
                      onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                      required
                      disabled={isLoading}
                    />
                    <Label htmlFor="terms" className="text-sm">
                      {t("auth.agreeToTerms")}{" "}
                      <Button variant="link" className="p-0 h-auto" disabled={isLoading}>
                        {t("auth.termsOfService")}
                      </Button>{" "}
                      {t("auth.and")}{" "}
                      <Button variant="link" className="p-0 h-auto" disabled={isLoading}>
                        {t("auth.privacyPolicy")}
                      </Button>
                    </Label>
                  </div>

                  <Button
                    type="submit"
                    className="w-full hover:bg-accent hover:text-accent-foreground transition-all duration-300 transform hover:scale-105"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("auth.creatingAccount")}
                      </>
                    ) : (
                      <>
                        {t("auth.createAccountButton")}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </form>

                <div className="text-center">
                  <p className="text-sm text-muted-foreground">
                    {t("auth.alreadyHaveAccount")}{" "}
                    <Button
                      variant="link"
                      className="p-0 h-auto"
                      onClick={() => setActiveTab("login")}
                      disabled={isLoading}
                    >
                      {t("auth.logInLink")}
                    </Button>
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        isOpen={forgotPasswordModalOpen}
        onClose={() => setForgotPasswordModalOpen(false)}
      />
    </div>
  )
}
