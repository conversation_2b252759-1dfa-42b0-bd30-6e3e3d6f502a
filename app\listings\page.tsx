"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { FeaturedCarCard } from "@/components/features/car/featured-car-card"
import { SearchIcon, Filter, Car, Bike, Heart } from "lucide-react"
import { useI18n } from "@/i18n/i18n-provider"
import { useSearchParams } from "next/navigation"
import Link from "next/link"

export default function ListingsPage() {
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [vehicleType, setVehicleType] = useState<"car" | "motorcycle">("car")
  const [selectedCity, setSelectedCity] = useState<string>("all")
  const [selectedColor, setSelectedColor] = useState<string>("all")
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 500])
  const [yearRange, setYearRange] = useState<[number, number]>([2015, 2024])
  const searchParams = useSearchParams()
  const [favorites, setFavorites] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 9

  const carColors = [
    "White", "Black", "Silver", "Gray", "Red", "Blue", "Green", "Yellow", "Orange", "Purple"
  ]

  function ColorPicker({ value, onChange }: { value: string; onChange: (color: string) => void }) {
    return (
      <div className="flex gap-2 flex-wrap">
        <button
          type="button"
          className={`w-8 h-8 rounded border-2 flex items-center justify-center focus:outline-none ${value === 'all' ? 'border-blue-600 ring-2 ring-blue-300' : 'border-gray-300'}`}
          style={{ backgroundColor: '#fff' }}
          aria-label="All Colors"
          onClick={() => onChange('all')}
        >
          {value === 'all' && <span className="w-3 h-3 rounded-full bg-blue-600" />}
        </button>
        {carColors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-8 h-8 rounded border-2 flex items-center justify-center focus:outline-none ${value === color ? 'border-blue-600 ring-2 ring-blue-300' : 'border-gray-300'}`}
            style={{ backgroundColor: color.toLowerCase() }}
            aria-label={color}
            onClick={() => onChange(color)}
          >
            {value === color && <span className="w-3 h-3 rounded-full bg-white border border-blue-600" />}
          </button>
        ))}
      </div>
    );
  }

  useEffect(() => {
    const typeParam = searchParams.get("type")
    if (typeParam === "motorcycle") {
      setVehicleType("motorcycle")
    }
  }, [searchParams])

  // Sample car data - in a real app, this would come from an API or database
  const carsData = [
    {
      id: "car1",
      title: "Tesla Model 3",
      price: 75,
      location: "Medina, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=Tesla+Front",
        "/placeholder.svg?height=400&width=600&text=Tesla+Side",
        "/placeholder.svg?height=400&width=600&text=Tesla+Interior",
      ],
      rating: 4.9,
      reviews: 234,
      category: "Electric",
      description: "Premium electric sedan with autopilot features",
    },
    {
      id: "car2",
      title: "BMW 3 Series",
      price: 85,
      location: "Gueliz, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=BMW+Front",
        "/placeholder.svg?height=400&width=600&text=BMW+Side",
        "/placeholder.svg?height=400&width=600&text=BMW+Interior",
      ],
      rating: 4.8,
      reviews: 189,
      category: "Luxury",
      description: "Luxury sports sedan with premium features",
    },
    {
      id: "car3",
      title: "Toyota RAV4",
      price: 60,
      location: "Palmeraie, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=RAV4+Front",
        "/placeholder.svg?height=400&width=600&text=RAV4+Side",
        "/placeholder.svg?height=400&width=600&text=RAV4+Interior",
      ],
      rating: 4.7,
      reviews: 312,
      category: "SUV",
      description: "Reliable SUV with spacious interior",
    },
    {
      id: "car4",
      title: "Honda Civic",
      price: 45,
      location: "Hivernage, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=Civic+Front",
        "/placeholder.svg?height=400&width=600&text=Civic+Side",
        "/placeholder.svg?height=400&width=600&text=Civic+Interior",
      ],
      rating: 4.6,
      reviews: 178,
      category: "Economy",
      description: "Fuel-efficient compact car",
    },
    {
      id: "car5",
      title: "Mercedes-Benz E-Class",
      price: 120,
      location: "Agdal, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=Mercedes+Front",
        "/placeholder.svg?height=400&width=600&text=Mercedes+Side",
        "/placeholder.svg?height=400&width=600&text=Mercedes+Interior",
      ],
      rating: 4.8,
      reviews: 156,
      category: "Luxury",
      description: "Executive luxury sedan with premium comfort",
    },
    {
      id: "car6",
      title: "Jeep Wrangler",
      price: 90,
      location: "Menara, Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=Jeep+Front",
        "/placeholder.svg?height=400&width=600&text=Jeep+Side",
        "/placeholder.svg?height=400&width=600&text=Jeep+Interior",
      ],
      rating: 4.5,
      reviews: 221,
      category: "SUV",
      description: "Rugged off-road SUV for adventure",
    },
    {
      id: "car7",
      title: "Audi A4",
      price: 95,
      location: "Rabat",
      images: [
        "/placeholder.svg?height=400&width=600&text=Audi+A4+Front",
        "/placeholder.svg?height=400&width=600&text=Audi+A4+Side",
      ],
      rating: 4.7,
      reviews: 210,
      category: "Luxury",
      description: "German engineering, comfort, and style.",
    },
    {
      id: "car8",
      title: "Hyundai Tucson",
      price: 70,
      location: "Casablanca",
      images: [
        "/placeholder.svg?height=400&width=600&text=Hyundai+Tucson+Front",
        "/placeholder.svg?height=400&width=600&text=Hyundai+Tucson+Side",
      ],
      rating: 4.4,
      reviews: 134,
      category: "SUV",
      description: "Modern SUV with advanced safety features.",
    },
    {
      id: "car9",
      title: "Peugeot 208",
      price: 40,
      location: "Fes",
      images: [
        "/placeholder.svg?height=400&width=600&text=Peugeot+208+Front",
        "/placeholder.svg?height=400&width=600&text=Peugeot+208+Side",
      ],
      rating: 4.3,
      reviews: 99,
      category: "Economy",
      description: "Compact and efficient for city driving.",
    },
    {
      id: "car10",
      title: "Dacia Duster",
      price: 55,
      location: "Tangier",
      images: [
        "/placeholder.svg?height=400&width=600&text=Dacia+Duster+Front",
        "/placeholder.svg?height=400&width=600&text=Dacia+Duster+Side",
      ],
      rating: 4.2,
      reviews: 88,
      category: "SUV",
      description: "Affordable SUV for all terrains.",
    },
    {
      id: "car11",
      title: "Renault Clio",
      price: 38,
      location: "Oujda",
      images: [
        "/placeholder.svg?height=400&width=600&text=Renault+Clio+Front",
        "/placeholder.svg?height=400&width=600&text=Renault+Clio+Side",
      ],
      rating: 4.1,
      reviews: 77,
      category: "Economy",
      description: "Popular hatchback, easy to drive and park.",
    },
  ]

  // Sample motorcycle data
  const motorcyclesData = [
    {
      id: "moto1",
      title: "Yamaha MT-07",
      price: 40,
      location: "Marrakech",
      images: [
        "/placeholder.svg?height=400&width=600&text=Yamaha+MT-07+Front",
        "/placeholder.svg?height=400&width=600&text=Yamaha+MT-07+Side",
      ],
      rating: 4.7,
      reviews: 98,
      category: "Naked",
      description: "Agile and powerful naked bike, perfect for city and touring.",
    },
    {
      id: "moto2",
      title: "Honda Africa Twin",
      price: 55,
      location: "Casablanca",
      images: [
        "/placeholder.svg?height=400&width=600&text=Africa+Twin+Front",
        "/placeholder.svg?height=400&width=600&text=Africa+Twin+Side",
      ],
      rating: 4.8,
      reviews: 120,
      category: "Adventure",
      description: "Legendary adventure motorcycle for long-distance rides.",
    },
    {
      id: "moto3",
      title: "KTM Duke 390",
      price: 35,
      location: "Agadir",
      images: [
        "/placeholder.svg?height=400&width=600&text=KTM+Duke+Front",
        "/placeholder.svg?height=400&width=600&text=KTM+Duke+Side",
      ],
      rating: 4.6,
      reviews: 75,
      category: "Sport",
      description: "Lightweight and sporty, ideal for city and fun rides.",
    },
    {
      id: "moto4",
      title: "Suzuki V-Strom 650",
      price: 50,
      location: "Rabat",
      images: [
        "/placeholder.svg?height=400&width=600&text=Suzuki+V-Strom+Front",
        "/placeholder.svg?height=400&width=600&text=Suzuki+V-Strom+Side",
      ],
      rating: 4.5,
      reviews: 80,
      category: "Touring",
      description: "Versatile touring motorcycle for long journeys.",
    },
    {
      id: "moto5",
      title: "Harley-Davidson Iron 883",
      price: 65,
      location: "Tangier",
      images: [
        "/placeholder.svg?height=400&width=600&text=Harley+Iron+883+Front",
        "/placeholder.svg?height=400&width=600&text=Harley+Iron+883+Side",
      ],
      rating: 4.9,
      reviews: 110,
      category: "Cruiser",
      description: "Classic American cruiser with iconic style.",
    },
    {
      id: "moto6",
      title: "Ducati Monster 821",
      price: 70,
      location: "Fes",
      images: [
        "/placeholder.svg?height=400&width=600&text=Ducati+Monster+Front",
        "/placeholder.svg?height=400&width=600&text=Ducati+Monster+Side",
      ],
      rating: 4.8,
      reviews: 95,
      category: "Naked",
      description: "Italian naked bike with sporty performance.",
    },
    {
      id: "moto7",
      title: "Kawasaki Z650",
      price: 42,
      location: "Kenitra",
      images: [
        "/placeholder.svg?height=400&width=600&text=Kawasaki+Z650+Front",
        "/placeholder.svg?height=400&width=600&text=Kawasaki+Z650+Side",
      ],
      rating: 4.4,
      reviews: 70,
      category: "Naked",
      description: "Balanced and fun, great for new riders.",
    },
    {
      id: "moto8",
      title: "Royal Enfield Himalayan",
      price: 48,
      location: "Oujda",
      images: [
        "/placeholder.svg?height=400&width=600&text=Royal+Enfield+Front",
        "/placeholder.svg?height=400&width=600&text=Royal+Enfield+Side",
      ],
      rating: 4.3,
      reviews: 60,
      category: "Adventure",
      description: "Rugged adventure bike for all terrains.",
    },
    {
      id: "moto9",
      title: "Honda CB500X",
      price: 52,
      location: "El Jadida",
      images: [
        "/placeholder.svg?height=400&width=600&text=Honda+CB500X+Front",
        "/placeholder.svg?height=400&width=600&text=Honda+CB500X+Side",
      ],
      rating: 4.5,
      reviews: 85,
      category: "Adventure",
      description: "Versatile and reliable for city and touring.",
    },
    {
      id: "moto10",
      title: "BMW F 850 GS",
      price: 75,
      location: "Beni Mellal",
      images: [
        "/placeholder.svg?height=400&width=600&text=BMW+F850GS+Front",
        "/placeholder.svg?height=400&width=600&text=BMW+F850GS+Side",
      ],
      rating: 4.9,
      reviews: 130,
      category: "Adventure",
      description: "Premium adventure bike for serious riders.",
    },
  ]

  // List of 30 major Moroccan cities, sorted A-Z
  const moroccanCities = [
    "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla",
    "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune",
    "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi",
    "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
  ].sort((a, b) => a.localeCompare(b))

  // Filter vehicles based on vehicleType, search term, and city
  const allVehicles = vehicleType === "car" ? carsData : motorcyclesData
  const filteredVehicles = allVehicles.filter((vehicle) => {
    const matchesSearch = !searchTerm.trim() ||
      vehicle.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCity = selectedCity === "all" || vehicle.location.includes(selectedCity)
    const matchesPrice = vehicle.price >= priceRange[0] && vehicle.price <= priceRange[1]
    const matchesYear = true
    return matchesSearch && matchesCity && matchesPrice && matchesYear
  })

  const totalPages = Math.ceil(filteredVehicles.length / pageSize)
  const paginatedVehicles = filteredVehicles.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Additional search logic could go here
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Filters Sidebar */}
        <div className="w-full md:w-64 space-y-6">
          <Card>
            <CardContent className="p-4 space-y-4">
              {/* Vehicle Type Filter */}
              <div className="flex justify-center gap-4 mb-4">
                <Button
                  variant={vehicleType === "car" ? "default" : "outline"}
                  className="flex flex-col items-center gap-1 px-4 py-2"
                  onClick={() => setVehicleType("car")}
                >
                  <Car className="h-6 w-6 mb-1" />
                  <span className="text-xs font-medium">Cars</span>
                </Button>
                <Button
                  variant={vehicleType === "motorcycle" ? "default" : "outline"}
                  className="flex flex-col items-center gap-1 px-4 py-2"
                  onClick={() => setVehicleType("motorcycle")}
                >
                  <Bike className="h-6 w-6 mb-1" />
                  <span className="text-xs font-medium">Motorcycles</span>
                </Button>
              </div>

              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">{t("listings.filters.title")}</h2>
                <Button variant="ghost" size="sm" className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  {t("listings.filters.clearAll")}
                </Button>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("listings.filters.priceRange")}</h3>
                <div className="space-y-4">
                  <Slider
                    value={priceRange}
                    onValueChange={(value: number[]) => setPriceRange(value as [number, number])}
                    min={0}
                    max={500}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between">
                    <span className="text-sm">MAD {priceRange[0]}</span>
                    <span className="text-sm">MAD {priceRange[1]}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Year Range</h3>
                <div className="space-y-4">
                  <Slider
                    value={yearRange}
                    onValueChange={(value: number[]) => setYearRange(value as [number, number])}
                    min={2010}
                    max={2024}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between">
                    <span className="text-sm">{yearRange[0]}</span>
                    <span className="text-sm">{yearRange[1]}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("listings.filters.carType")}</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="suv" />
                    <Label htmlFor="suv">{t("carType.suv")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sedan" />
                    <Label htmlFor="sedan">{t("carType.sedan")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="luxury" />
                    <Label htmlFor="luxury">{t("carType.luxury")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="electric" />
                    <Label htmlFor="electric">{t("carType.electric")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="hybrid" />
                    <Label htmlFor="hybrid">{t("carType.hybrid")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="economy" />
                    <Label htmlFor="economy">{t("carType.economy")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="compact" />
                    <Label htmlFor="compact">{t("carType.compact")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="convertible" />
                    <Label htmlFor="convertible">{t("carType.convertible")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="minivan" />
                    <Label htmlFor="minivan">{t("carType.minivan")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="pickup" />
                    <Label htmlFor="pickup">{t("carType.pickup")}</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sport" />
                    <Label htmlFor="sport">{t("carType.sport")}</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("listings.filters.features")}</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="automatic" />
                    <Label htmlFor="automatic">Automatic</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="manual" />
                    <Label htmlFor="manual">Manual</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="gps" />
                    <Label htmlFor="gps">GPS</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="bluetooth" />
                    <Label htmlFor="bluetooth">Bluetooth</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="child-seat" />
                    <Label htmlFor="child-seat">Child Seat</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">{t("listings.filters.rating")}</h3>
                <Select defaultValue="any">
                  <SelectTrigger>
                    <SelectValue placeholder="Any rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any rating</SelectItem>
                    <SelectItem value="4.5">4.5+</SelectItem>
                    <SelectItem value="4.0">4.0+</SelectItem>
                    <SelectItem value="3.5">3.5+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* City Filter */}
              <div className="space-y-2">
                <h3 className="font-medium">City</h3>
                <Select value={selectedCity} onValueChange={setSelectedCity}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="All Cities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cities</SelectItem>
                    {moroccanCities.map((city) => (
                      <SelectItem key={city} value={city}>{city}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Color Filter */}
              <div className="space-y-2">
                <h3 className="font-medium">Color</h3>
                <ColorPicker value={selectedColor} onChange={setSelectedColor} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search Results */}
        <div className="flex-1 space-y-6">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{vehicleType === "car" ? t("listings.title") : "Available Motorcycles"}</h1>
              <p className="text-muted-foreground">{vehicleType === "car"
                ? t("listings.carsFound", { count: filteredVehicles.length })
                : `${filteredVehicles.length} motorcycles found`}</p>
            </div>
            <form onSubmit={handleSearchSubmit} className="flex gap-2 w-full md:w-auto">
              <div className="relative flex-1 md:flex-initial">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  className="pl-8"
                  placeholder={t("listings.search.placeholder")}
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
              </div>
              <Select defaultValue="recommended">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="recommended">{t("listings.sort.recommended")}</SelectItem>
                  <SelectItem value="price-low">{t("listings.sort.priceLow")}</SelectItem>
                  <SelectItem value="price-high">{t("listings.sort.priceHigh")}</SelectItem>
                  <SelectItem value="rating-high">{t("listings.sort.ratingHigh")}</SelectItem>
                </SelectContent>
              </Select>
            </form>
          </div>

          {paginatedVehicles.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {paginatedVehicles.map((vehicle) => (
                  <Link key={vehicle.id} href={`/listings/${vehicle.id.startsWith('car') ? 'car-details' : 'motorcycle-details'}/${vehicle.id}`}>
                    <div className="relative">
                      <FeaturedCarCard
                        id={vehicle.id}
                        title={vehicle.title}
                        price={vehicle.price}
                        location={vehicle.location}
                        images={vehicle.images}
                        rating={vehicle.rating}
                        reviews={vehicle.reviews}
                        category={vehicle.category}
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        aria-label="Favorite"
                        onClick={(e) => {
                          e.preventDefault()
                          setFavorites(favs => favs.includes(vehicle.id) ? favs.filter(id => id !== vehicle.id) : [...favs, vehicle.id])
                        }}
                        className="absolute top-2 right-2 z-10"
                      >
                        <Heart className={favorites.includes(vehicle.id) ? "fill-red-500 text-red-500" : "text-gray-400"} />
                      </Button>
                    </div>
                  </Link>
                ))}
              </div>
              {/* Pagination Controls */}
              <div className="flex justify-center mt-8">
                <nav className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                    aria-label="Previous page"
                  >
                    &lt;
                  </Button>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <Button
                      key={page}
                      variant={page === currentPage ? "default" : "ghost"}
                      className={page === currentPage ? "bg-blue-900 text-white rounded-full" : "rounded-full"}
                      size="icon"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  ))}
                  <Button
                    variant="ghost"
                    size="icon"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                    aria-label="Next page"
                  >
                    &gt;
                  </Button>
                </nav>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <SearchIcon className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium mb-2">{t("listings.noResults.title")}</h3>
              <p className="text-muted-foreground">{t("listings.noResults.subtitle")}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
