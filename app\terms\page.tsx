"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { FileTex<PERSON>, <PERSON><PERSON>Triangle, CheckCircle, Clock, DollarSign, Shield } from "lucide-react"

export default function TermsPage() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="p-3 bg-green-100 rounded-full">
                            <FileText className="h-8 w-8 text-green-600" />
                        </div>
                    </div>
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">Terms & Conditions</h1>
                    <p className="text-lg text-gray-600">Last updated: January 2025</p>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                                Acceptance of Terms
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600">
                                By accessing and using morentcar's car rental platform, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use our services.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="h-5 w-5 text-blue-600" />
                                Eligibility Requirements
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Driver Requirements</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Must be at least 21 years old</li>
                                    <li>Valid driver's license for at least 2 years</li>
                                    <li>Clean driving record with no major violations</li>
                                    <li>Valid credit card for security deposit</li>
                                    <li>Proof of identity and address</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">International Drivers</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>International driving permit required</li>
                                    <li>Valid passport or national ID</li>
                                    <li>Additional insurance may be required</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5 text-yellow-600" />
                                Booking and Payment Terms
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Booking Process</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Bookings must be made at least 24 hours in advance</li>
                                    <li>Full payment required at time of booking</li>
                                    <li>Security deposit required for vehicle pickup</li>
                                    <li>Valid credit card must be presented at pickup</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Payment Methods</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Credit cards (Visa, Mastercard, American Express)</li>
                                    <li>Debit cards with sufficient funds</li>
                                    <li>Digital wallets (Apple Pay, Google Pay)</li>
                                    <li>Bank transfers (for advance bookings)</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5 text-purple-600" />
                                Rental Period and Extensions
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Rental Duration</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Minimum rental period: 1 day</li>
                                    <li>Maximum rental period: 30 days</li>
                                    <li>Extensions subject to vehicle availability</li>
                                    <li>Early returns may incur additional fees</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Extension Policy</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Extensions must be requested 24 hours in advance</li>
                                    <li>Additional charges apply for extensions</li>
                                    <li>Extensions subject to vehicle availability</li>
                                    <li>No extensions during peak seasons without prior notice</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-red-600" />
                                Vehicle Use and Restrictions
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Prohibited Uses</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Off-road driving or racing</li>
                                    <li>Transporting hazardous materials</li>
                                    <li>Smoking in vehicles</li>
                                    <li>Transporting pets without prior approval</li>
                                    <li>Crossing international borders without permission</li>
                                    <li>Using vehicle for commercial purposes</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Vehicle Care</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Return vehicle in same condition as received</li>
                                    <li>Report any damage immediately</li>
                                    <li>Maintain proper fuel levels</li>
                                    <li>Follow recommended maintenance schedules</li>
                                    <li>Keep vehicle clean and tidy</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Cancellation and Refund Policy</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Cancellation Fees</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Free cancellation up to 48 hours before pickup</li>
                                    <li>25% fee for cancellations 24-48 hours before pickup</li>
                                    <li>50% fee for cancellations 12-24 hours before pickup</li>
                                    <li>No refund for cancellations less than 12 hours before pickup</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Refund Processing</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Refunds processed within 5-7 business days</li>
                                    <li>Refunds issued to original payment method</li>
                                    <li>Security deposits refunded after vehicle inspection</li>
                                    <li>Processing fees may apply to refunds</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Insurance and Liability</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-gray-600 mb-4">
                                All rentals include basic insurance coverage. Additional insurance options are available:
                            </p>
                            <ul className="list-disc list-inside space-y-2 text-gray-600">
                                <li>Collision Damage Waiver (CDW)</li>
                                <li>Theft Protection</li>
                                <li>Personal Accident Insurance</li>
                                <li>Roadside Assistance</li>
                                <li>Third Party Liability Coverage</li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Contact Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600 mb-4">
                                For questions about these Terms and Conditions, please contact us:
                            </p>
                            <div className="space-y-2 text-gray-600">
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Phone:</strong> +212 524 123 456</p>
                                <p><strong>Address:</strong> 123, Rue Mohammed V, Marrakech, Morocco</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
} 