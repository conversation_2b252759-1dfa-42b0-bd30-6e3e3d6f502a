"use client"

import React, { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Car, Calendar, MapPin, Bell, Settings, User, Heart, Clock, Star, TrendingUp, Activity, Zap, Eye } from "lucide-react"
import { useI18n } from "@/i18n/i18n-provider"
import { DashboardLayout } from "@/components/layouts/dashboard-layout"
import { useRouter, useSearchParams } from "next/navigation"
import { NotificationsPanel } from "@/components/user/notifications-panel"
import { SettingsPanel } from "@/components/user/settings-panel"
import { useBookings } from '@/contexts/bookings-context'
import { toast } from "sonner"

const sidebarNavItems = [
  {
    title: "Dashboard",
    href: "/user/dashboard",
    icon: <User className="h-4 w-4" />,
  },
  {
    title: "My Bookings",
    href: "/user/dashboard?tab=bookings",
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    title: "Favorites",
    href: "/user/dashboard?tab=favorites",
    icon: <Heart className="h-4 w-4" />,
  },
  {
    title: "Rental History",
    href: "/user/dashboard?tab=history",
    icon: <Clock className="h-4 w-4" />,
  },
  {
    title: "Settings",
    href: "/user/dashboard?tab=settings",
    icon: <Settings className="h-4 w-4" />,
  },
  {
    title: "Notifications",
    href: "/user/dashboard?tab=notifications",
    icon: <Bell className="h-4 w-4" />,
  },
]

export default function UserDashboard() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { t } = useI18n()
  const router = useRouter()
  const searchParams = useSearchParams()
  const tab = searchParams.get("tab") || "bookings"
  const message = searchParams.get("message")
  const { bookings, addBooking, deleteBooking, updateBooking } = useBookings()
  const [isLoadingDashboard, setIsLoadingDashboard] = useState(true)

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setIsLoadingDashboard(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  // Check for complete registration message
  useEffect(() => {
    if (message === "complete-registration") {
      toast.success(t("auth.completeRegistrationMessage"))
      // Remove the message from URL
      const newUrl = new URL(window.location.href)
      newUrl.searchParams.delete("message")
      window.history.replaceState({}, "", newUrl.toString())
    }
  }, [message, t])

  // Show loading spinner while auth state is loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect or block access if not authenticated
  if (!isAuthenticated || !user) {
    if (typeof window !== "undefined") {
      window.location.replace("/auth")
    }
    return null
  }

  // Filter bookings for the current user (after user is confirmed not null)
  const userBookings = bookings.filter(b => b.userId === user.id)

  // Enhanced stats with animations
  const stats = [
    {
      title: "Upcoming Bookings",
      value: "3",
      icon: Calendar,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      title: "Favorite Locations",
      value: "4",
      icon: MapPin,
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      title: "Notifications",
      value: "7",
      icon: Bell,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      title: "Total Rentals",
      value: "12",
      icon: Car,
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600"
    }
  ]

  if (isLoadingDashboard) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <DashboardLayout sidebarNavItems={sidebarNavItems} title="User Dashboard">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col md:flex-row gap-6 md:items-center mb-8">
            <Avatar className="h-24 w-24 shadow-lg">
              <AvatarImage src={"/placeholder.svg"} alt={`${user.first_name} ${user.last_name}`} />
              <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-2xl">
                {user.first_name ? user.first_name.charAt(0) : "U"}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {t("dashboard.welcome")}, {user.first_name} {user.last_name}
              </h1>
              <p className="text-muted-foreground text-lg">{user.email}</p>
              <div className="flex items-center gap-4">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <Activity className="w-3 h-3 mr-1" />
                  Active Member
                </Badge>
                <Badge variant="outline" className="border-blue-300 text-blue-600">
                  <Star className="w-3 h-3 mr-1" />
                  Premium User
                </Badge>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-0 shadow-md"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                      <h3 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        {stat.value}
                      </h3>
                    </div>
                    <div className={`p-3 rounded-full ${stat.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                      <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {tab === "bookings" && (
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                <CardTitle className="flex items-center gap-3 text-blue-800">
                  <Calendar className="h-6 w-6" />
                  Current & Upcoming Bookings
                </CardTitle>
                <CardDescription className="text-blue-700">
                  View and manage your current and upcoming car rentals.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {userBookings.length === 0 ? (
                    <div className="text-center py-12">
                      <Car className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-muted-foreground mb-2">No bookings found</h3>
                      <p className="text-muted-foreground mb-4">Start your journey by booking your first car!</p>
                      <Button onClick={() => router.push('/listings')} className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
                        <Zap className="w-4 h-4 mr-2" />
                        Browse Cars
                      </Button>
                    </div>
                  ) : (
                    userBookings.map((booking, index) => (
                      <div key={booking.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow duration-200 bg-white">
                        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                          <div className="w-full md:w-24 h-16 bg-gradient-to-r from-blue-100 to-purple-100 rounded-md overflow-hidden flex items-center justify-center">
                            <Car className="h-8 w-8 text-blue-600" />
                          </div>
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-lg">{booking.carId}</h3>
                              <Badge variant="outline" className="text-xs">
                                BK-{booking.id.slice(-6).toUpperCase()}
                              </Badge>
                            </div>
                            <div className="flex flex-col md:flex-row md:items-center gap-1 md:gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Calendar className="w-4 h-4" />
                                {booking.startDate} - {booking.endDate}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="w-4 h-4" />
                                Marrakech
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm" className="border-green-300 text-green-600 hover:bg-green-50">
                              <Eye className="w-4 h-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-300 text-red-600 hover:bg-red-50"
                              onClick={() => deleteBooking(booking.id)}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {tab === "favorites" && (
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-pink-50 to-red-50 border-b">
                <CardTitle className="flex items-center gap-3 text-pink-800">
                  <Heart className="h-6 w-6" />
                  {t("dashboard.favorites")}
                </CardTitle>
                <CardDescription className="text-pink-700">
                  Cars you've saved to your favorites list.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 bg-white group">
                    <div className="h-40 bg-gradient-to-r from-blue-100 to-purple-100 flex items-center justify-center">
                      <Car className="h-16 w-16 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg mb-2">Mercedes-Benz E-Class</h3>
                      <p className="text-sm text-muted-foreground mb-3">Luxury • MAD 1,200/day</p>
                      <div className="flex items-center gap-2 mb-3">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium">4.8</span>
                        <span className="text-sm text-muted-foreground">(156 reviews)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-blue-600">Rabat</span>
                        <Button variant="outline" size="sm" className="border-blue-300 text-blue-600 hover:bg-blue-50">
                          View
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200 bg-white group">
                    <div className="h-40 bg-gradient-to-r from-green-100 to-blue-100 flex items-center justify-center">
                      <Car className="h-16 w-16 text-green-600 group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold text-lg mb-2">Range Rover Sport</h3>
                      <p className="text-sm text-muted-foreground mb-3">SUV • MAD 1,800/day</p>
                      <div className="flex items-center gap-2 mb-3">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium">4.9</span>
                        <span className="text-sm text-muted-foreground">(89 reviews)</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-green-600">Casablanca</span>
                        <Button variant="outline" size="sm" className="border-green-300 text-green-600 hover:bg-green-50">
                          View
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {tab === "history" && (
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 border-b">
                <CardTitle className="flex items-center gap-3 text-gray-800">
                  <Clock className="h-6 w-6" />
                  Rental History
                </CardTitle>
                <CardDescription className="text-gray-700">
                  Your past car rental experiences.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="border rounded-lg p-4 bg-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">BMW 3 Series</h3>
                        <p className="text-sm text-muted-foreground">March 15-18, 2024 • Marrakech</p>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Completed
                      </Badge>
                    </div>
                  </div>
                  <div className="border rounded-lg p-4 bg-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-semibold">Toyota RAV4</h3>
                        <p className="text-sm text-muted-foreground">February 10-12, 2024 • Casablanca</p>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Completed
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {tab === "settings" && <SettingsPanel />}
          {tab === "notifications" && <NotificationsPanel />}
        </div>
      </div>
    </DashboardLayout>
  )
}
