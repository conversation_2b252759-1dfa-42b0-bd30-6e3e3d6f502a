"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Shield<PERSON>he<PERSON>, Lock } from "lucide-react"

export default function AdminLoginPage() {
    const router = useRouter()
    const [secretKey, setSecretKey] = useState("")
    const [isLoading, setIsLoading] = useState(false)

    const handleAdminAccess = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsLoading(true)

        try {
            // Check against the admin secret
            if (secretKey === "admin-secret-123") {
                // Set admin session in both localStorage and cookies for consistency
                localStorage.setItem("adminAuthenticated", "true")
                localStorage.setItem(
                    "user",
                    JSON.stringify({
                        id: "admin-1",
                        name: "Admin User",
                        email: "<EMAIL>",
                        role: "admin",
                    }),
                )

                // Set cookie with proper attributes for middleware
                document.cookie = "adminAuthenticated=true; path=/; max-age=86400; SameSite=Lax"

                toast.success("Admin access granted")
                setTimeout(() => {
                    router.push("/admin/dashboard")
                }, 500)
            } else {
                toast.error("Invalid admin key")
            }
        } catch (error) {
            toast.error("Authentication failed")
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
            <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg">
                <div className="text-center">
                    <ShieldCheck className="mx-auto h-12 w-12 text-primary" />
                    <h2 className="mt-6 text-3xl font-bold text-gray-900">Admin Access</h2>
                    <p className="mt-2 text-sm text-gray-600">Enter admin key to continue</p>
                </div>

                <form className="mt-8 space-y-6" onSubmit={handleAdminAccess}>
                    <div className="rounded-md shadow-sm -space-y-px">
                        <div className="relative">
                            <Label htmlFor="admin-key" className="sr-only">
                                Admin Key
                            </Label>
                            <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                            <Input
                                id="admin-key"
                                name="adminKey"
                                type="password"
                                required
                                className="pl-10 py-3"
                                placeholder="Enter admin key"
                                value={secretKey}
                                onChange={(e) => setSecretKey(e.target.value)}
                            />
                        </div>
                    </div>

                    <div>
                        <Button type="submit" className="w-full py-3 bg-black hover:bg-gray-800 text-white" disabled={isLoading}>
                            {isLoading ? "Authenticating..." : "Access Admin Panel"}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    )
} 
