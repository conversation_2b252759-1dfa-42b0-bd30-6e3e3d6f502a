import { NextResponse } from 'next/server'

// Use the same admin secret as in ADMIN_SETUP.md
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'password123'
const ADMIN_SECRET = 'admin-secret-123'

export async function POST(req: Request) {
    const { email, password, secretKey } = await req.json()
    if (
        email === ADMIN_EMAIL &&
        password === ADMIN_PASSWORD &&
        secretKey === ADMIN_SECRET
    ) {
        const res = NextResponse.json({ 
            success: true,
            user: {
                id: "admin-1",
                name: "Admin User",
                email: ADMIN_EMAIL,
                role: "admin",
            }
        })
        
        // Set cookie with proper attributes
        res.cookies.set('adminAuthenticated', 'true', {
            path: '/',
            maxAge: 60 * 60 * 24, // 24 hours
            httpOnly: true,
            sameSite: 'lax',
            secure: process.env.NODE_ENV === 'production', // Only secure in production
        })
        
        return res
    }
    
    return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 })
} 
