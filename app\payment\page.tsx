"use client"

import { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { CheckCircle } from "lucide-react"
import { usePaymentRequests } from "@/contexts/payment-requests-context"

export default function PaymentPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const plan = searchParams.get("plan") || "monthly"
    const { addPaymentRequest } = usePaymentRequests()

    const [agencyName, setAgencyName] = useState("")
    const [ownerName, setOwnerName] = useState("")
    const [email, setEmail] = useState("")
    const [phone, setPhone] = useState("")
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [showSuccessDialog, setShowSuccessDialog] = useState(false)

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)
        try {
            if (!agencyName || !ownerName || !email || !phone) {
                alert("Please fill in all fields.")
                setIsSubmitting(false)
                return
            }
            // Save pricing request
            addPaymentRequest({
                agencyName,
                ownerName,
                email,
                phone,
                plan,
                receiptUrl: "",
                date: new Date().toISOString()
            })
            setShowSuccessDialog(true)
        } catch (err) {
            alert("Failed to submit pricing request.")
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleSuccessDialogClose = () => {
        setShowSuccessDialog(false)
        router.push("/")
    }

    return (
        <div className="container mx-auto py-12 px-4">
            <div className="max-w-xl mx-auto">
                <Card>
                    <CardContent className="p-8">
                        <h1 className="text-2xl font-bold mb-6">Request Pricing Information</h1>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="agencyName">Agency Name</Label>
                                <Input id="agencyName" value={agencyName} onChange={e => setAgencyName(e.target.value)} required />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="ownerName">Owner Name</Label>
                                <Input id="ownerName" value={ownerName} onChange={e => setOwnerName(e.target.value)} required />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <Input id="email" type="email" value={email} onChange={e => setEmail(e.target.value)} required />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number</Label>
                                <Input id="phone" value={phone} onChange={e => setPhone(e.target.value)} required />
                            </div>
                            <div className="space-y-2">
                                <Label>Interested Plan</Label>
                                <Input value={plan === "6months" ? "6-Month Plan" : plan === "yearly" ? "Yearly Plan" : "Monthly Plan"} readOnly />
                            </div>
                            <Button type="submit" className="w-full" disabled={isSubmitting}>
                                {isSubmitting ? "Submitting..." : "Request Pricing"}
                            </Button>
                        </form>
                    </CardContent>
                </Card>
            </div>

            {/* Success Dialog */}
            <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                            Request Sent Successfully!
                        </DialogTitle>
                        <DialogDescription className="pt-4">
                            <div className="space-y-4">
                                <p className="text-lg font-medium text-green-600">
                                    Your request has been sent!
                                </p>
                                <p className="text-gray-600">
                                    Thank you for your interest in our platform. Our team will contact you within 24-48 hours to discuss pricing options and plans that best suit your agency's needs.
                                </p>
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <p className="text-sm text-blue-800">
                                        <strong>What happens next:</strong>
                                    </p>
                                    <ul className="text-sm text-blue-700 mt-2 space-y-1">
                                        <li>• We'll review your agency details</li>
                                        <li>• Our team will contact you to discuss pricing</li>
                                        <li>• We'll provide customized plan options</li>
                                        <li>• You can ask questions and get clarification</li>
                                    </ul>
                                </div>
                            </div>
                        </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end">
                        <Button onClick={handleSuccessDialogClose} className="bg-green-600 hover:bg-green-700">
                            Continue to Home
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
} 