"use client"

import { usePaymentRequests } from "@/contexts/payment-requests-context"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function AdminPaymentRequestsPage() {
    const { paymentRequests } = usePaymentRequests()

    return (
        <div className="p-6 space-y-6">
            <h1 className="text-3xl font-bold mb-6">Pricing Requests</h1>
            {paymentRequests.length === 0 ? (
                <div className="text-center text-muted-foreground py-12">No pricing requests yet.</div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {paymentRequests.map((req, i) => (
                        <Card key={i}>
                            <CardContent className="p-6 space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="font-semibold">{req.agencyName}</span>
                                    <Badge variant={req.status === "pending" ? "secondary" : req.status === "approved" ? "default" : "destructive"}>
                                        {req.status.charAt(0).toUpperCase() + req.status.slice(1)}
                                    </Badge>
                                </div>
                                <div className="text-sm text-muted-foreground">{new Date(req.date).toLocaleString()}</div>
                                <div><span className="font-medium">Owner:</span> {req.ownerName}</div>
                                <div><span className="font-medium">Email:</span> {req.email}</div>
                                <div><span className="font-medium">Phone:</span> {req.phone}</div>
                                <div><span className="font-medium">Plan:</span> {req.plan === "6months" ? "6-Month Plan" : req.plan === "yearly" ? "Yearly Plan" : "Monthly Plan"}</div>
                                <div>
                                    <span className="font-medium">Receipt:</span><br />
                                    <img src={req.receiptUrl} alt="Receipt" className="mt-2 h-32 rounded border" />
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    )
} 