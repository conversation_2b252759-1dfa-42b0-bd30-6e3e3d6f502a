export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      agencies: {
        Row: {
          id: string
          user_id: string
          agency_name: string
          agency_description: string | null
          agency_address: string | null
          agency_phone: string | null
          agency_email: string | null
          agency_website: string | null
          agency_logo: string | null
          is_approved: boolean
          rating: number | null
          total_bookings: number
          location: unknown | null
          business_hours: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          agency_name: string
          agency_description?: string | null
          agency_address?: string | null
          agency_phone?: string | null
          agency_email?: string | null
          agency_website?: string | null
          agency_logo?: string | null
          is_approved?: boolean
          rating?: number | null
          total_bookings?: number
          location?: unknown | null
          business_hours?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          agency_name?: string
          agency_description?: string | null
          agency_address?: string | null
          agency_phone?: string | null
          agency_email?: string | null
          agency_website?: string | null
          agency_logo?: string | null
          is_approved?: boolean
          rating?: number | null
          total_bookings?: number
          location?: unknown | null
          business_hours?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      bookings: {
        Row: {
          id: string
          user_id: string
          car_id: string
          agency_id: string
          start_date: string
          end_date: string
          pickup_location: string
          return_location: string
          total_price: number
          status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'rejected'
          payment_status: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
          special_requests: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          car_id: string
          agency_id: string
          start_date: string
          end_date: string
          pickup_location: string
          return_location: string
          total_price: number
          status?: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'rejected'
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
          special_requests?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          car_id?: string
          agency_id?: string
          start_date?: string
          end_date?: string
          pickup_location?: string
          return_location?: string
          total_price?: number
          status?: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled' | 'rejected'
          payment_status?: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded'
          special_requests?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      cars: {
        Row: {
          id: string
          agency_id: string
          brand: string
          model: string
          year: number
          license_plate: string
          color: string
          fuel_type: 'gasoline' | 'diesel' | 'electric' | 'hybrid' | 'plug-in_hybrid' | 'hydrogen'
          transmission: 'manual' | 'automatic' | 'semi_automatic'
          seats: number
          doors: number
          daily_rate: number
          weekly_rate: number | null
          monthly_rate: number | null
          status: 'available' | 'rented' | 'maintenance' | 'unavailable' | 'reserved'
          description: string | null
          features: string[]
          images: string[]
          location: unknown | null
          address: string | null
          mileage: number
          insurance_info: Json | null
          gps_device_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          agency_id: string
          brand: string
          model: string
          year: number
          license_plate: string
          color: string
          fuel_type: 'gasoline' | 'diesel' | 'electric' | 'hybrid' | 'plug-in_hybrid' | 'hydrogen'
          transmission: 'manual' | 'automatic' | 'semi_automatic'
          seats: number
          doors: number
          daily_rate: number
          weekly_rate?: number | null
          monthly_rate?: number | null
          status?: 'available' | 'rented' | 'maintenance' | 'unavailable' | 'reserved'
          description?: string | null
          features?: string[]
          images?: string[]
          location?: unknown | null
          address?: string | null
          mileage?: number
          insurance_info?: Json | null
          gps_device_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          agency_id?: string
          brand?: string
          model?: string
          year?: number
          license_plate?: string
          color?: string
          fuel_type?: 'gasoline' | 'diesel' | 'electric' | 'hybrid' | 'plug-in_hybrid' | 'hydrogen'
          transmission?: 'manual' | 'automatic' | 'semi_automatic'
          seats?: number
          doors?: number
          daily_rate?: number
          weekly_rate?: number | null
          monthly_rate?: number | null
          status?: 'available' | 'rented' | 'maintenance' | 'unavailable' | 'reserved'
          description?: string | null
          features?: string[]
          images?: string[]
          location?: unknown | null
          address?: string | null
          mileage?: number
          insurance_info?: Json | null
          gps_device_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      coupons: {
        Row: {
          id: string
          agency_id: string
          code: string
          discount_type: 'percentage' | 'fixed'
          discount_value: number
          min_amount: number
          max_discount: number | null
          usage_limit: number | null
          used_count: number
          valid_from: string
          valid_until: string | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          agency_id: string
          code: string
          discount_type: 'percentage' | 'fixed'
          discount_value: number
          min_amount?: number
          max_discount?: number | null
          usage_limit?: number | null
          used_count?: number
          valid_from?: string
          valid_until?: string | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          agency_id?: string
          code?: string
          discount_type?: 'percentage' | 'fixed'
          discount_value?: number
          min_amount?: number
          max_discount?: number | null
          usage_limit?: number | null
          used_count?: number
          valid_from?: string
          valid_until?: string | null
          is_active?: boolean
          created_at?: string
        }
      }
      gps_tracking: {
        Row: {
          id: string
          car_id: string
          latitude: number
          longitude: number
          speed: number | null
          heading: number | null
          battery_level: number | null
          is_online: boolean
          timestamp: string
        }
        Insert: {
          id?: string
          car_id: string
          latitude: number
          longitude: number
          speed?: number | null
          heading?: number | null
          battery_level?: number | null
          is_online?: boolean
          timestamp?: string
        }
        Update: {
          id?: string
          car_id?: string
          latitude?: number
          longitude?: number
          speed?: number | null
          heading?: number | null
          battery_level?: number | null
          is_online?: boolean
          timestamp?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          message: string
          type: 'booking' | 'payment' | 'review' | 'system'
          is_read: boolean
          data: Json | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          message: string
          type: 'booking' | 'payment' | 'review' | 'system'
          is_read?: boolean
          data?: Json | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          message?: string
          type?: 'booking' | 'payment' | 'review' | 'system'
          is_read?: boolean
          data?: Json | null
          created_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          booking_id: string
          amount: number
          currency: string
          payment_method: string
          transaction_id: string | null
          status: 'pending' | 'completed' | 'failed' | 'refunded'
          payment_date: string
          created_at: string
        }
        Insert: {
          id?: string
          booking_id: string
          amount: number
          currency?: string
          payment_method: string
          transaction_id?: string | null
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          payment_date?: string
          created_at?: string
        }
        Update: {
          id?: string
          booking_id?: string
          amount?: number
          currency?: string
          payment_method?: string
          transaction_id?: string | null
          status?: 'pending' | 'completed' | 'failed' | 'refunded'
          payment_date?: string
          created_at?: string
        }
      }
      reviews: {
        Row: {
          id: string
          booking_id: string
          user_id: string
          agency_id: string
          car_id: string
          rating: number
          comment: string | null
          status: 'pending' | 'approved' | 'rejected'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          booking_id: string
          user_id: string
          agency_id: string
          car_id: string
          rating: number
          comment?: string | null
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          booking_id?: string
          user_id?: string
          agency_id?: string
          car_id?: string
          rating?: number
          comment?: string | null
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          phone: string | null
          avatar: string | null
          role: 'user' | 'agency' | 'admin'
          is_verified: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name: string
          last_name: string
          phone?: string | null
          avatar?: string | null
          role?: 'user' | 'agency' | 'admin'
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          first_name?: string
          last_name?: string
          phone?: string | null
          avatar?: string | null
          role?: 'user' | 'agency' | 'admin'
          is_verified?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
